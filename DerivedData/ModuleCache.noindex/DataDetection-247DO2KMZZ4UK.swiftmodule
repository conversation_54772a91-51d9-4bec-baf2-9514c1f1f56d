---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/DataDetection.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1733472390000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/DataDetection.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
    size:            19064
  - mtime:           1731231567000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            2083728
    sdk_relative:    true
  - mtime:           1731232120000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1731227095000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1731228246000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1731230793000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1731228628000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1731228215000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1731232346000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            3713
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            998
    sdk_relative:    true
  - mtime:           1731232380000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            999
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            962
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1424
    sdk_relative:    true
  - mtime:           1731232386000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            694
    sdk_relative:    true
  - mtime:           1731232346000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            23967
    sdk_relative:    true
  - mtime:           1731231626000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            5810
    sdk_relative:    true
  - mtime:           1731232399000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            19776
    sdk_relative:    true
  - mtime:           1731232885000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            248688
    sdk_relative:    true
  - mtime:           1731233177000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22665
    sdk_relative:    true
  - mtime:           1731234258000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            172315
    sdk_relative:    true
  - mtime:           1731234228000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            6430
    sdk_relative:    true
  - mtime:           1731234408000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            56579
    sdk_relative:    true
  - mtime:           1731234573000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22798
    sdk_relative:    true
  - mtime:           1731234578000000000
    path:            'usr/lib/swift/XPC.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            33154
    sdk_relative:    true
  - mtime:           1731232910000000000
    path:            'usr/lib/swift/Observation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            3741
    sdk_relative:    true
  - mtime:           1731234295000000000
    path:            'usr/lib/swift/System.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            94365
    sdk_relative:    true
  - mtime:           1731235070000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            998797
    sdk_relative:    true
  - mtime:           1731235340000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            579
    sdk_relative:    true
version:         1
...
