---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/SwiftUI.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
dependencies:
  - mtime:           1733472485000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator/prebuilt-modules/18.2/SwiftUI.swiftmodule/x86_64-apple-ios-simulator.swiftmodule'
    size:            3342024
  - mtime:           1731231567000000000
    path:            'usr/lib/swift/Swift.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            2083728
    sdk_relative:    true
  - mtime:           1731232120000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1731232346000000000
    path:            'usr/lib/swift/_errno.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            3713
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_signal.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            998
    sdk_relative:    true
  - mtime:           1731232380000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            999
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_time.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            962
    sdk_relative:    true
  - mtime:           1731232367000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1424
    sdk_relative:    true
  - mtime:           1731232386000000000
    path:            'usr/lib/swift/unistd.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            694
    sdk_relative:    true
  - mtime:           1731232346000000000
    path:            'usr/lib/swift/_math.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            23967
    sdk_relative:    true
  - mtime:           1731231626000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            5810
    sdk_relative:    true
  - mtime:           1731232399000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            19776
    sdk_relative:    true
  - mtime:           1731232885000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            248688
    sdk_relative:    true
  - mtime:           1731233177000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22665
    sdk_relative:    true
  - mtime:           1731234258000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            172315
    sdk_relative:    true
  - mtime:           1731227095000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1731228246000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1731230793000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1731228628000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1731228215000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1731227770000000000
    path:            'System/Library/Frameworks/CoreData.framework/Headers/CoreData.apinotes'
    size:            7789
    sdk_relative:    true
  - mtime:           1731234228000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            6430
    sdk_relative:    true
  - mtime:           1731234408000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            56579
    sdk_relative:    true
  - mtime:           1731234573000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22798
    sdk_relative:    true
  - mtime:           1731234578000000000
    path:            'usr/lib/swift/XPC.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            33154
    sdk_relative:    true
  - mtime:           1731232910000000000
    path:            'usr/lib/swift/Observation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            3741
    sdk_relative:    true
  - mtime:           1731234295000000000
    path:            'usr/lib/swift/System.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            94365
    sdk_relative:    true
  - mtime:           1731235070000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            998797
    sdk_relative:    true
  - mtime:           1731235394000000000
    path:            'System/Library/Frameworks/CoreData.framework/Modules/CoreData.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            42480
    sdk_relative:    true
  - mtime:           1731227987000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Headers/CoreGraphics.apinotes'
    size:            52901
    sdk_relative:    true
  - mtime:           1731654096000000000
    path:            'System/Library/Frameworks/CoreGraphics.framework/Modules/CoreGraphics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            52739
    sdk_relative:    true
  - mtime:           1731236699000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1731235320000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            20457
    sdk_relative:    true
  - mtime:           1731232120000000000
    path:            'usr/include/os.apinotes'
    size:            1658
    sdk_relative:    true
  - mtime:           1731234575000000000
    path:            'usr/lib/swift/os.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            107940
    sdk_relative:    true
  - mtime:           1731237655000000000
    path:            'System/Library/Frameworks/CoreTransferable.framework/Modules/CoreTransferable.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            22306
    sdk_relative:    true
  - mtime:           1731235974000000000
    path:            'System/Library/Frameworks/DeveloperToolsSupport.framework/Modules/DeveloperToolsSupport.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            10765
    sdk_relative:    true
  - mtime:           1731235386000000000
    path:            'usr/lib/swift/OSLog.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1143
    sdk_relative:    true
  - mtime:           1731234262000000000
    path:            'usr/lib/swift/simd.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            218604
    sdk_relative:    true
  - mtime:           1731234411000000000
    path:            'usr/lib/swift/Spatial.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            99060
    sdk_relative:    true
  - mtime:           1731235658000000000
    path:            'System/Library/Frameworks/OpenGLES.framework/Headers/OpenGLES.apinotes'
    size:            1192
    sdk_relative:    true
  - mtime:           1730064096000000000
    path:            'System/Library/Frameworks/Metal.framework/Headers/Metal.apinotes'
    size:            77528
    sdk_relative:    true
  - mtime:           1731230309000000000
    path:            'System/Library/Frameworks/CoreImage.framework/Headers/CoreImage.apinotes'
    size:            36883
    sdk_relative:    true
  - mtime:           1731236712000000000
    path:            'System/Library/Frameworks/CoreText.framework/Headers/CoreText.apinotes'
    size:            1662
    sdk_relative:    true
  - mtime:           1731228760000000000
    path:            'System/Library/Frameworks/QuartzCore.framework/Headers/QuartzCore.apinotes'
    size:            7428
    sdk_relative:    true
  - mtime:           1731236700000000000
    path:            'System/Library/Frameworks/UserNotifications.framework/Headers/UserNotifications.apinotes'
    size:            326
    sdk_relative:    true
  - mtime:           1731587397000000000
    path:            'System/Library/Frameworks/UIKit.framework/Headers/UIKit.apinotes'
    size:            162201
    sdk_relative:    true
  - mtime:           1731301196000000000
    path:            'System/Library/Frameworks/Accessibility.framework/Modules/Accessibility.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            21167
    sdk_relative:    true
  - mtime:           1731236954000000000
    path:            'System/Library/Frameworks/CoreText.framework/Modules/CoreText.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1426
    sdk_relative:    true
  - mtime:           1731235340000000000
    path:            'usr/lib/swift/DataDetection.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            579
    sdk_relative:    true
  - mtime:           1731235474000000000
    path:            'usr/lib/swift/Metal.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            25854
    sdk_relative:    true
  - mtime:           1731235969000000000
    path:            'usr/lib/swift/QuartzCore.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1654
    sdk_relative:    true
  - mtime:           1731236902000000000
    path:            'System/Library/Frameworks/Symbols.framework/Modules/Symbols.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            21441
    sdk_relative:    true
  - mtime:           1731235949000000000
    path:            'usr/lib/swift/FileProvider.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1373
    sdk_relative:    true
  - mtime:           1731235946000000000
    path:            'usr/lib/swift/CoreImage.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            585
    sdk_relative:    true
  - mtime:           1731587450000000000
    path:            'System/Library/Frameworks/UIKit.framework/Modules/UIKit.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            219835
    sdk_relative:    true
  - mtime:           1731301280000000000
    path:            'System/Library/Frameworks/SwiftUICore.framework/Modules/SwiftUICore.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1007555
    sdk_relative:    true
  - mtime:           1731303388000000000
    path:            'System/Library/Frameworks/SwiftUI.framework/Modules/SwiftUI.swiftmodule/x86_64-apple-ios-simulator.swiftinterface'
    size:            1322630
    sdk_relative:    true
version:         1
...
