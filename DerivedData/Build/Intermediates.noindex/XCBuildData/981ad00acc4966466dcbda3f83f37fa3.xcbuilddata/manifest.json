{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex": {"is-mutated": true}, "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products": {"is-mutated": true}, "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator": {"is-mutated": true}, "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app": {"is-mutated": true}, "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban": {"is-mutated": true}, "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib": {"is-mutated": true}, "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib": {"is-mutated": true}, "<TRIGGER: CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib normal>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/_CodeSignature", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<Linked Binary Debug Dylib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib>", "<target-SuperSokoban-****************************************************************--begin-scanning>", "<target-SuperSokoban-****************************************************************--end>", "<target-SuperSokoban-****************************************************************--linker-inputs-ready>", "<target-SuperSokoban-****************************************************************--modules-ready>", "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"], "outputs": ["<all>"]}, "<target-SuperSokoban-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/_CodeSignature", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SparkParticle.sks", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/button_press.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/error.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/failure.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/push.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/reset.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/undo.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/win.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Assets.car", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_signature", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/Main.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/PkgInfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_lto.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.hmap"], "roots": ["/tmp/SuperSokoban.dst", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products"], "outputs": ["<target-SuperSokoban-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>"]}, "<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban-7eee3bf7631ebb48b327684c83e1cb72-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<workspace-Debug-iphonesimulator18.2-iphonesimulator--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "inputs": [], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache", "<ClangStatCache /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban.xcodeproj", "signature": "f72a6419a31c76e0cd149fdb225971cd"}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products"]}, "P0:::CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban-7eee3bf7631ebb48b327684c83e1cb72-VFS-iphonesimulator/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/ssu/root.ssu.yaml", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Metadata.appintents>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList"], "outputs": ["<target-SuperSokoban-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-ChangePermissions>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-StripSymbols>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib>", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-GenerateStubAPI>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ProductPostprocessingTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-CodeSign>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-Validate>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-CopyAside>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<Validate /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "outputs": ["<target-SuperSokoban-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--GeneratedFilesTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ProductStructureTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist"], "outputs": ["<target-SuperSokoban-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--RealityAssetsTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.hmap"], "outputs": ["<target-SuperSokoban-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/PkgInfo"], "outputs": ["<target-SuperSokoban-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--HeadermapTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleMapTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-SuperSokoban-****************************************************************--InfoPlistTaskProducer>", "<target-SuperSokoban-****************************************************************--VersionPlistTaskProducer>", "<target-SuperSokoban-****************************************************************--SanitizerTaskProducer>", "<target-SuperSokoban-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-SuperSokoban-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-SuperSokoban-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-SuperSokoban-****************************************************************--StubBinaryTaskProducer>", "<target-SuperSokoban-****************************************************************--TestTargetTaskProducer>", "<target-SuperSokoban-****************************************************************--TestHostTaskProducer>", "<target-SuperSokoban-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-SuperSokoban-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-SuperSokoban-****************************************************************--DocumentationTaskProducer>", "<target-SuperSokoban-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--start>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "outputs": ["<target-SuperSokoban-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<CopySwiftStdlib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "outputs": ["<target-SuperSokoban-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ProductPostprocessingTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--VersionPlistTaskProducer": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--VersionPlistTaskProducer>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-SuperSokoban-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main-SBPartialInfo.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SparkParticle.sks", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/button_press.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/error.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/failure.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/push.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/reset.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/undo.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/win.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Assets.car", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_signature", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/LaunchScreen.storyboardc/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/Main.storyboardc/", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_lto.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt"], "outputs": ["<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h"], "outputs": ["<target-SuperSokoban-****************************************************************--generated-headers>"]}, "P0:::Gate target-SuperSokoban-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h"], "outputs": ["<target-SuperSokoban-****************************************************************--swift-generated-headers>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:AppIntentsSSUTraining": {"tool": "shell", "description": "AppIntentsSSUTraining", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Metadata.appintents>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/ssu/root.ssu.yaml"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsnltrainingprocessor", "--infoplist-path", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist", "--temp-dir-path", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/ssu", "--bundle-id", "brush.com.SuperSokoban", "--product-path", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "--extracted-metadata-path", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Metadata.appintents", "--archive-ssu-assets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "signature": "dd13cd0fa3084c56837e2e3ff64cb963"}, "P0:target-SuperSokoban-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AppDelegate.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AudioManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/LaunchScreen.storyboard/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/Main.storyboard/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/DeadlockDetector.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameScene.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameStateManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/LevelGenerator.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SceneDelegate.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SparkParticle.sks/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Tests/SuperSokobanTests.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/UIManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/ViewController.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/button_press.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/error.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/failure.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/push.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/reset.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/undo.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/win.wav/", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib>", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>", "<target-SuperSokoban-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban normal>", "<TRIGGER: MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/_CodeSignature", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AppDelegate.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AudioManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/LaunchScreen.storyboard/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/Main.storyboard/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/DeadlockDetector.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameScene.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameStateManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/LevelGenerator.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SceneDelegate.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SparkParticle.sks/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Tests/SuperSokobanTests.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/UIManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/ViewController.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/button_press.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/error.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/failure.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/push.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/reset.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/undo.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/win.wav/", "<target-SuperSokoban-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AppDelegate.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AudioManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/LaunchScreen.storyboard/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/Main.storyboard/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/DeadlockDetector.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameScene.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameStateManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/LevelGenerator.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SceneDelegate.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SparkParticle.sks/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Tests/SuperSokobanTests.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/UIManager.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/ViewController.swift/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/button_press.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/error.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/failure.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/push.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/reset.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/undo.wav/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/win.wav/", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib>", "<target-SuperSokoban-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--entry>", "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib normal>"], "outputs": ["<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant thinned /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_dependencies_thinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--filter-for-thinning-device-configuration", "iPhone17,3", "--filter-for-device-os-version", "18.2", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "control-enabled": false, "signature": "de16c79bf7dd57747e9a4c61383b5d22"}, "P0:target-SuperSokoban-****************************************************************-:Debug:CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets": {"tool": "shell", "description": "CompileAssetCatalogVariant unthinned /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_dependencies_unthinned", "--output-partial-info-plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "control-enabled": false, "signature": "d0d32af063ea2b03cfcac7222f5a76ca"}, "P0:target-SuperSokoban-****************************************************************-:Debug:CompileStoryboard /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/LaunchScreen.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/LaunchScreen.storyboard", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/LaunchScreen.storyboard", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "SuperSokoban", "--output-partial-info-plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--output-format", "human-readable-text", "--compilation-directory", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/LaunchScreen.storyboard"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "control-enabled": false, "signature": "24ea53ac5821fb8b165785fabbd0784c"}, "P0:target-SuperSokoban-****************************************************************-:Debug:CompileStoryboard /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/Main.storyboard": {"tool": "shell", "description": "CompileStoryboard /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/Main.storyboard", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/Main.storyboard", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main-SBPartialInfo.plist"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "SuperSokoban", "--output-partial-info-plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main-SBPartialInfo.plist", "--auto-activate-custom-fonts", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--output-format", "human-readable-text", "--compilation-directory", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Base.lproj/Main.storyboard"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "control-enabled": false, "signature": "52a44cb7c23b2e6f2e5c7bdd1508c28e"}, "P0:target-SuperSokoban-****************************************************************-:Debug:CopySwiftLibs /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app": {"tool": "embed-swift-stdlib", "description": "CopySwiftLibs /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["<CopySwiftStdlib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "deps": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SwiftStdLibToolInputDependencies.dep"}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SparkParticle.sks /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SparkParticle.sks": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SparkParticle.sks /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SparkParticle.sks", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SparkParticle.sks/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SparkParticle.sks"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/button_press.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/button_press.wav": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/button_press.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/button_press.wav", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/button_press.wav/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/button_press.wav"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/error.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/error.wav": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/error.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/error.wav", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/error.wav/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/error.wav"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/failure.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/failure.wav": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/failure.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/failure.wav", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/failure.wav/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/failure.wav"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/push.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/push.wav": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/push.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/push.wav", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/push.wav/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/push.wav"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/reset.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/reset.wav": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/reset.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/reset.wav", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/reset.wav/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/reset.wav"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/undo.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/undo.wav": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/undo.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/undo.wav", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/undo.wav/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/undo.wav"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/win.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/win.wav": {"tool": "file-copy", "description": "CpResource /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/win.wav /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/win.wav", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/win.wav/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/win.wav"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "appintents-metadata", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Tests/SuperSokobanTests.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AppDelegate.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AudioManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/DeadlockDetector.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameScene.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameStateManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/LevelGenerator.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SceneDelegate.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/UIManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/ViewController.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Metadata.appintents>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SuperSokoban.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-SuperSokoban-****************************************************************--begin-compiling>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SuperSokoban.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-SuperSokoban-****************************************************************--begin-linking>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SuperSokoban.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--begin-scanning>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--end": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--entry>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/ssu/root.ssu.yaml", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib>", "<CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main-SBPartialInfo.plist", "<CopySwiftStdlib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SparkParticle.sks", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/button_press.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/error.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/failure.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/push.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/reset.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/undo.wav", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/win.wav", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Metadata.appintents>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Assets.car", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_signature", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/LaunchScreen.storyboardc/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/Main.storyboardc/", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned>", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/PkgInfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "<RegisterExecutionPolicyException /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "<Touch /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "<Validate /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_lto.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-non-framework-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.hmap", "<target-SuperSokoban-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-SuperSokoban-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-SuperSokoban-****************************************************************--Barrier-ChangePermissions>", "<target-SuperSokoban-****************************************************************--Barrier-CodeSign>", "<target-SuperSokoban-****************************************************************--Barrier-CopyAside>", "<target-SuperSokoban-****************************************************************--Barrier-GenerateStubAPI>", "<target-SuperSokoban-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-SuperSokoban-****************************************************************--Barrier-RegisterProduct>", "<target-SuperSokoban-****************************************************************--Barrier-StripSymbols>", "<target-SuperSokoban-****************************************************************--Barrier-Validate>", "<target-SuperSokoban-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-SuperSokoban-****************************************************************--DocumentationTaskProducer>", "<target-SuperSokoban-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-SuperSokoban-****************************************************************--GeneratedFilesTaskProducer>", "<target-SuperSokoban-****************************************************************--HeadermapTaskProducer>", "<target-SuperSokoban-****************************************************************--InfoPlistTaskProducer>", "<target-SuperSokoban-****************************************************************--ModuleMapTaskProducer>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--ProductPostprocessingTaskProducer>", "<target-SuperSokoban-****************************************************************--ProductStructureTaskProducer>", "<target-SuperSokoban-****************************************************************--RealityAssetsTaskProducer>", "<target-SuperSokoban-****************************************************************--SanitizerTaskProducer>", "<target-SuperSokoban-****************************************************************--StubBinaryTaskProducer>", "<target-SuperSokoban-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-SuperSokoban-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-SuperSokoban-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-SuperSokoban-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-SuperSokoban-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-SuperSokoban-****************************************************************--TestHostTaskProducer>", "<target-SuperSokoban-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-SuperSokoban-****************************************************************--TestTargetTaskProducer>", "<target-SuperSokoban-****************************************************************--VersionPlistTaskProducer>", "<target-SuperSokoban-****************************************************************--copy-headers-completion>", "<target-SuperSokoban-****************************************************************--fused-phase0-compile-sources&link-binary&copy-bundle-resources>", "<target-SuperSokoban-****************************************************************--generated-headers>", "<target-SuperSokoban-****************************************************************--swift-generated-headers>"], "outputs": ["<target-SuperSokoban-****************************************************************--end>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SuperSokoban.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>", "<target-SuperSokoban-****************************************************************--begin-compiling>"], "outputs": ["<target-SuperSokoban-****************************************************************--entry>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************-Debug-iphonesimulator--x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/SuperSokoban.dst>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator>", "<CreateBuildDirectory-/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator>"], "outputs": ["<target-SuperSokoban-****************************************************************--immediate>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "<Linked Binary /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_lto.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt"], "outputs": ["<target-SuperSokoban-****************************************************************--linker-inputs-ready>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h"], "outputs": ["<target-SuperSokoban-****************************************************************--modules-ready>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--begin-compiling>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/ssu/root.ssu.yaml", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned/", "<CopySwiftStdlib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "<ExtractAppIntentsMetadata /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Metadata.appintents>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Assets.car", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_signature", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned>", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule", "<Linked Binary /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_lto.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList", "<target-SuperSokoban-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-SuperSokoban-****************************************************************--unsigned-product-ready>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Gate target-SuperSokoban-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-SuperSokoban-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-SuperSokoban-****************************************************************--will-sign>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:GenerateAssetSymbols /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets": {"tool": "shell", "description": "GenerateAssetSymbols /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets/", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/actool", "--output-format", "human-readable-text", "--notices", "--warnings", "--export-dependency-info", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_dependencies", "--output-partial-info-plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "--app-icon", "AppIcon", "--accent-color", "AccentColor", "--compress-pngs", "--enable-on-demand-resources", "YES", "--development-region", "en", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--platform", "iphonesimulator", "--compile", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets", "--bundle-identifier", "brush.com.SuperSokoban", "--generate-swift-asset-symbols", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "--generate-objc-asset-symbols", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.h", "--generate-asset-symbol-index", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols-Index.plist"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "control-enabled": false, "signature": "372a49eb588b39b8375967bbce581dd4"}, "P0:target-SuperSokoban-****************************************************************-:Debug:LinkAssetCatalog /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets": {"tool": "link-assetcatalog", "description": "LinkAssetCatalog /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Assets.xcassets/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_signature", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Assets.car"], "deps": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_dependencies"}, "P0:target-SuperSokoban-****************************************************************-:Debug:LinkAssetCatalogSignature": {"tool": "link-assetcatalog", "description": "LinkAssetCatalogSignature", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_signature"], "always-out-of-date": true}, "P0:target-SuperSokoban-****************************************************************-:Debug:LinkStoryboards": {"tool": "shell", "description": "LinkStoryboards", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main.storyboardc", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/LaunchScreen.storyboardc/", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Base.lproj/Main.storyboardc/"], "args": ["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool", "--errors", "--warnings", "--notices", "--module", "SuperSokoban", "--target-device", "iphone", "--target-device", "ipad", "--minimum-deployment-target", "18.2", "--output-format", "human-readable-text", "--link", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen.storyboardc", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main.storyboardc"], "env": {"XCODE_DEVELOPER_USR_PATH": "/Applications/Xcode.app/Contents/Developer/usr/bin/.."}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "control-enabled": false, "signature": "9ca5d7f03b06747ee3356262841534e3"}, "P0:target-SuperSokoban-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/thinned>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_output/unthinned>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "inputs": ["<target-SuperSokoban-****************************************************************--start>", "<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "<MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>", "<TRIGGER: MkDir /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Info.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist /Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Info.plist", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/LaunchScreen-SBPartialInfo.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/assetcatalog_generated_info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Base.lproj/Main-SBPartialInfo.plist", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/PkgInfo"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:ProcessProductPackaging  /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent": {"tool": "process-product-entitlements", "description": "ProcessProductPackaging  /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist", "<target-SuperSokoban-****************************************************************--ProductStructureTaskProducer>", "<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:ProcessProductPackagingDER /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der": {"tool": "shell", "description": "ProcessProductPackagingDER /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "<target-SuperSokoban-****************************************************************--ProductStructureTaskProducer>", "<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der"], "args": ["/usr/bin/derq", "query", "-f", "xml", "-i", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "-o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "--raw"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "signature": "99a328a788177975266d7be883c16c1c"}, "P0:target-SuperSokoban-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "<target-SuperSokoban-****************************************************************--Barrier-CodeSign>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:SwiftDriver Compilation SuperSokoban normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation SuperSokoban normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Tests/SuperSokobanTests.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AppDelegate.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AudioManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/DeadlockDetector.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameScene.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameStateManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/LevelGenerator.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SceneDelegate.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/UIManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/ViewController.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-SuperSokoban-****************************************************************--generated-headers>", "<target-SuperSokoban-****************************************************************--copy-headers-completion>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.stringsdata", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.swiftconstvalues", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.swiftconstvalues"]}, "P0:target-SuperSokoban-****************************************************************-:Debug:Touch /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app": {"tool": "shell", "description": "Touch /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "<target-SuperSokoban-****************************************************************--Barrier-Validate>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "signature": "d9527880603ab0161d99fa002e758079"}, "P0:target-SuperSokoban-****************************************************************-:Debug:Validate /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app": {"tool": "validate-product", "description": "Validate /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/Info.plist", "<target-SuperSokoban-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-SuperSokoban-****************************************************************--will-sign>", "<target-SuperSokoban-****************************************************************--entry>", "<TRIGGER: CodeSign /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"], "outputs": ["<Validate /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app>"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban-7eee3bf7631ebb48b327684c83e1cb72-VFS-iphonesimulator/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban-7eee3bf7631ebb48b327684c83e1cb72-VFS-iphonesimulator/all-product-headers.yaml", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban-7eee3bf7631ebb48b327684c83e1cb72-VFS-iphonesimulator/all-product-headers.yaml"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:ConstructStubExecutorLinkFileList /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt": {"tool": "construct-stub-executor-input-file-list", "description": "ConstructStubExecutorLinkFileList /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "inputs": ["/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor_no_swift_entry_point.a", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/usr/lib/libPreviewsJITStubExecutor.a", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo/", "<target-SuperSokoban-****************************************************************--copy-headers-completion>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json/", "<target-SuperSokoban-****************************************************************--copy-headers-completion>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.abi.json"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc/", "<target-SuperSokoban-****************************************************************--copy-headers-completion>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftdoc"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule/", "<target-SuperSokoban-****************************************************************--copy-headers-completion>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.swiftmodule/x86_64-apple-ios-simulator.swiftmodule"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban normal", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban", "<Linked Binary /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban>", "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-ios18.2-simulator", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-e", "___debug_blank_executor_main", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_dylib", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__debug_instlnm", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "-<PERSON><PERSON><PERSON>", "-filelist", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-ExecutorLinkFileList-normal-x86_64.txt", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "-o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "signature": "d47649113d7538b8d6da1e0cf3c9e4f4"}, "P2:target-SuperSokoban-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib normal", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokobanTests.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AppDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/AudioManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/DeadlockDetector.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameScene.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GameStateManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/LevelGenerator.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SceneDelegate.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/UIManager.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/ViewController.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/GeneratedAssetSymbols.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "<target-SuperSokoban-****************************************************************--generated-headers>", "<target-SuperSokoban-****************************************************************--swift-generated-headers>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib", "<Linked Binary Debug Dylib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_lto.o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-L/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/EagerLinkingTBDs/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "-filelist", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "-install_name", "@rpath/SuperSokoban.debug.dylib", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_lto.o", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphonesimulator", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "-<PERSON><PERSON><PERSON>", "-alias", "-<PERSON><PERSON><PERSON>", "_main", "-<PERSON><PERSON><PERSON>", "___debug_main_executable_dylib_entry_point", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat", "-o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/SuperSokoban.debug.dylib"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "deps": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_dependency_info.dat"], "deps-style": "dependency-info", "signature": "9a9f962b458158cd424709fa1568b891"}, "P2:target-SuperSokoban-****************************************************************-:Debug:Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib normal": {"tool": "shell", "description": "Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib normal", "inputs": ["<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib", "<Linked Binary Preview Injection Dylib /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib>", "<TRIGGER: Ld /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib normal>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-ios18.2-simulator", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.2.sdk", "-O0", "-L/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "-F/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator", "-install_name", "@rpath/SuperSokoban.debug.dylib", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-export_dynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-objc_abi_version", "-<PERSON><PERSON><PERSON>", "2", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__entitlements", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent", "-<PERSON><PERSON><PERSON>", "-sectcreate", "-<PERSON><PERSON><PERSON>", "__TEXT", "-<PERSON><PERSON><PERSON>", "__ents_der", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.app-Simulated.xcent.der", "-o", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products/Debug-iphonesimulator/SuperSokoban.app/__preview.dylib"], "env": {}, "working-directory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "signature": "49d32dc8500851d6606564ed5214deae"}, "P2:target-SuperSokoban-****************************************************************-:Debug:SwiftDriver Compilation Requirements SuperSokoban normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements SuperSokoban normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/Tests/SuperSokobanTests.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AppDelegate.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/AudioManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/DeadlockDetector.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameScene.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/GameStateManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/LevelGenerator.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/SceneDelegate.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/UIManager.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban/ViewController.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/GeneratedAssetSymbols.swift", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap", "<ClangStatCache /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/SDKStatCaches.noindex/iphonesimulator18.2-22C146-07b28473f605e47e75261259d3ef3b5a.sdkstatcache>", "<target-SuperSokoban-****************************************************************--copy-headers-completion>", "<target-SuperSokoban-****************************************************************--ModuleVerifierTaskProducer>", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban Swift Compilation Requirements Finished", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftmodule", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftsourceinfo", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.abi.json", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.swiftdoc"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "inputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-Swift.h", "<target-SuperSokoban-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/SuperSokoban-Swift.h"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/DerivedSources/Entitlements-Simulated.plist"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban-OutputFileMap.json"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.LinkFileList"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftConstValuesFileList"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban.SwiftFileList"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/Objects-normal/x86_64/SuperSokoban_const_extract_protocols.json"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibInstallName-normal-x86_64.txt"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-DebugDylibPath-normal-x86_64.txt"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-non-framework-target-headers.hmap", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-non-framework-target-headers.hmap"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-all-target-headers.hmap"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-generated-files.hmap"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-own-target-headers.hmap"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban-project-headers.hmap"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.DependencyMetadataFileList"]}, "P2:target-SuperSokoban-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.hmap", "inputs": ["<target-SuperSokoban-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/SuperSokoban.build/Debug-iphonesimulator/SuperSokoban.build/SuperSokoban.hmap"]}}}