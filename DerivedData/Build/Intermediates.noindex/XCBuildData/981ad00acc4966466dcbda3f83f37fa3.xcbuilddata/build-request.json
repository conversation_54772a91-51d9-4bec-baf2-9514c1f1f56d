{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "7eee3bf7631ebb48b327684c83e1cb72a223eeeeda09d692f4f53d34e47b4051"}], "containerPath": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban.xcodeproj", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "x86_64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphonesimulator", "sdk": "iphonesimulator18.2", "sdkVariant": "iphonesimulator", "supportedArchitectures": ["x86_64"], "targetArchitecture": "x86_64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Products", "derivedDataPath": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/DerivedData/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {}}, "synthesized": {"table": {"ACTION": "build", "ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone17,3", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "18.2", "ASSETCATALOG_FILTER_FOR_THINNING_DEVICE_CONFIGURATION": "iPhone17,3", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "COLOR_DIAGNOSTICS": "YES", "diagnostic_message_length": "35", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES", "ONLY_ACTIVE_ARCH": "YES", "TARGET_DEVICE_IDENTIFIER": "F4D90F24-4724-4C02-A240-32DEBFFCF6AD", "TARGET_DEVICE_MODEL": "iPhone17,3", "TARGET_DEVICE_OS_VERSION": "18.2", "TARGET_DEVICE_PLATFORM_NAME": "iphonesimulator"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}