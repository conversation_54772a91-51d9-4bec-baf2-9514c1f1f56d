{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "7eee3bf7631ebb48b327684c83e1cb72cb783cc6c914facb6fee5a81912eba98", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.2", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "7eee3bf7631ebb48b327684c83e1cb72b9467562b748e84759bb3b4cfd317fcb", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb7246db7d5b65eb5df9fd9775a9108df00b", "path": "SuperSokobanTests.swift", "sourceTree": "<group>", "type": "file"}], "guid": "7eee3bf7631ebb48b327684c83e1cb72bf8af5fe003d033ac582e07a5b5b63d0", "name": "Tests", "path": "Tests", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb72e062ae4124092eacc4d5c9bd1ba03fac", "path": "AppDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "7eee3bf7631ebb48b327684c83e1cb72ad4403afd6a6fef7fae9aa0922d57680", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb72fe36a92038304a5c40600ab4ea034f31", "path": "AudioManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "audio.wav", "guid": "7eee3bf7631ebb48b327684c83e1cb723a1db823e49bbe2c91ffc75db2618ca6", "path": "button_press.wav", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb72908248cf600a6604dbe5e432fb541174", "path": "DeadlockDetector.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "audio.wav", "guid": "7eee3bf7631ebb48b327684c83e1cb72b99bf2050fe2c3e161bb51d27ffff937", "path": "error.wav", "sourceTree": "<group>", "type": "file"}, {"fileType": "audio.wav", "guid": "7eee3bf7631ebb48b327684c83e1cb7253fb53b37d6fee03f904523409bcecf3", "path": "failure.wav", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb7281c3367bad12eeab2f050b0c67117b4f", "path": "GameScene.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb72868cc49322c5299cef080e1cae9575c9", "path": "GameStateManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "7eee3bf7631ebb48b327684c83e1cb720f62acca47229d1e41de8c12064a2128", "path": "Info.plist", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "7eee3bf7631ebb48b327684c83e1cb72a0340788cb50602d291dc7dcce550293", "path": "Base.lproj/LaunchScreen.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "7eee3bf7631ebb48b327684c83e1cb72f1ebf85638d6e74a8d28184d5913c320", "name": "LaunchScreen.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb72d19ae17dca972060202fc348fd352dca", "path": "LevelGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "file.storyboard", "guid": "7eee3bf7631ebb48b327684c83e1cb7229a7cd01d9e6461880f7ac0e4f3327d8", "path": "Base.lproj/Main.storyboard", "regionVariantName": "Base", "sourceTree": "<group>", "type": "file"}], "guid": "7eee3bf7631ebb48b327684c83e1cb72ce13a647081d7bbd2d887ff33be930a4", "name": "Main.storyboard", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "audio.wav", "guid": "7eee3bf7631ebb48b327684c83e1cb726bf8a1fb5b14f7ecbcbdd774cdda6362", "path": "push.wav", "sourceTree": "<group>", "type": "file"}, {"fileType": "audio.wav", "guid": "7eee3bf7631ebb48b327684c83e1cb7243db8ce44a03bb25b641b18320b45c52", "path": "reset.wav", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb727ed87b195e534d98f3a98633362f1433", "path": "SceneDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "file.sks", "guid": "7eee3bf7631ebb48b327684c83e1cb721668f5a961a50f28dfc50a038b923247", "path": "SparkParticle.sks", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb7214cb9a46123731416053e59db2489538", "path": "UIManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "audio.wav", "guid": "7eee3bf7631ebb48b327684c83e1cb72a59f9b322edd85cac430cf9078750c53", "path": "undo.wav", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "7eee3bf7631ebb48b327684c83e1cb723190a040a92f69f3d0ae254fdbeec5c9", "path": "ViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "audio.wav", "guid": "7eee3bf7631ebb48b327684c83e1cb725ab7fc32ac9d2882d5c9452af342409a", "path": "win.wav", "sourceTree": "<group>", "type": "file"}], "guid": "7eee3bf7631ebb48b327684c83e1cb726e0e9b1c0c5ec9961596eb6ef791a31a", "name": "SuperSokoban", "path": "SuperSokoban", "sourceTree": "<group>", "type": "group"}, {"guid": "7eee3bf7631ebb48b327684c83e1cb72dc38c785dfc4b3ed663e098c273f2d66", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "7eee3bf7631ebb48b327684c83e1cb721d9b8ac4a99785b87b4f817afcb34b8e", "name": "SuperSokoban", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "7eee3bf7631ebb48b327684c83e1cb72", "path": "/Users/<USER>/Desktop/augmentCode/SuperSokoban/SuperSokoban.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/augmentCode/SuperSokoban", "targets": ["TARGET@v11_hash=a02012996d8c87ce0725cd5b13827526"]}