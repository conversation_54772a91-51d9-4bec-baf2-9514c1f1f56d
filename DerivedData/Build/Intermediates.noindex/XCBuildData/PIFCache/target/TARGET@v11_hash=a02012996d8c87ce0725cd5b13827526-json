{"buildConfigurations": [{"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "E3X3GY5696", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "SuperSokoban/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UIMainStoryboardFile": "Main", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "brush.com.SuperSokoban", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "7eee3bf7631ebb48b327684c83e1cb7224ad5861e6175296196d5f3d81317037", "name": "Debug"}, {"buildSettings": {"ASSETCATALOG_COMPILER_APPICON_NAME": "AppIcon", "ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME": "AccentColor", "CODE_SIGN_STYLE": "Automatic", "CURRENT_PROJECT_VERSION": "1", "DEVELOPMENT_TEAM": "E3X3GY5696", "GENERATE_INFOPLIST_FILE": "YES", "INFOPLIST_FILE": "SuperSokoban/Info.plist", "INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents": "YES", "INFOPLIST_KEY_UILaunchStoryboardName": "LaunchScreen", "INFOPLIST_KEY_UIMainStoryboardFile": "Main", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad": "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone": "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks", "MARKETING_VERSION": "1.0", "PRODUCT_BUNDLE_IDENTIFIER": "brush.com.SuperSokoban", "PRODUCT_NAME": "$(TARGET_NAME)", "SWIFT_EMIT_LOC_STRINGS": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "7eee3bf7631ebb48b327684c83e1cb72228e0c74f74643d9930eebe3a5d4f494", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "7eee3bf7631ebb48b327684c83e1cb7246db7d5b65eb5df9fd9775a9108df00b", "guid": "7eee3bf7631ebb48b327684c83e1cb72f12eb03367309082c1f562759d1a22ee"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72e062ae4124092eacc4d5c9bd1ba03fac", "guid": "7eee3bf7631ebb48b327684c83e1cb72d97867d3b0854049f0497536d377b737"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72fe36a92038304a5c40600ab4ea034f31", "guid": "7eee3bf7631ebb48b327684c83e1cb725c22626acbf8897341ffe035158755a8"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72908248cf600a6604dbe5e432fb541174", "guid": "7eee3bf7631ebb48b327684c83e1cb72df3b5bee0727f792d231d4eb3e25e7f3"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb7281c3367bad12eeab2f050b0c67117b4f", "guid": "7eee3bf7631ebb48b327684c83e1cb72784e902879b350153bafcddbcbc5234f"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72868cc49322c5299cef080e1cae9575c9", "guid": "7eee3bf7631ebb48b327684c83e1cb727787c315388595ab77877be2e3d19459"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72d19ae17dca972060202fc348fd352dca", "guid": "7eee3bf7631ebb48b327684c83e1cb7290ae76d5702b1802fe74588d0274751e"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb727ed87b195e534d98f3a98633362f1433", "guid": "7eee3bf7631ebb48b327684c83e1cb72648161d4670e5bbe93c2af2f10392742"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb7214cb9a46123731416053e59db2489538", "guid": "7eee3bf7631ebb48b327684c83e1cb72eb1beca59c375fd2324c9f6566b44789"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb723190a040a92f69f3d0ae254fdbeec5c9", "guid": "7eee3bf7631ebb48b327684c83e1cb721a84c69119120fbbdf23089827b5d31b"}], "guid": "7eee3bf7631ebb48b327684c83e1cb72b2e2cd7dfc3b1e496c2ef87429c7ce05", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "7eee3bf7631ebb48b327684c83e1cb72fa5f62537072c532993db5d20bc21613", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "7eee3bf7631ebb48b327684c83e1cb72ad4403afd6a6fef7fae9aa0922d57680", "guid": "7eee3bf7631ebb48b327684c83e1cb72558c91557d3cfe83299ff6e03038b369"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb723a1db823e49bbe2c91ffc75db2618ca6", "guid": "7eee3bf7631ebb48b327684c83e1cb720c3c9a77800e02d260b3aca90b7e26af"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72b99bf2050fe2c3e161bb51d27ffff937", "guid": "7eee3bf7631ebb48b327684c83e1cb721f905ffbac24f58cc5001e9c50d7baa2"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb7253fb53b37d6fee03f904523409bcecf3", "guid": "7eee3bf7631ebb48b327684c83e1cb72b1eba50f82be44942fd5b8ee259948aa"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72f1ebf85638d6e74a8d28184d5913c320", "guid": "7eee3bf7631ebb48b327684c83e1cb729f48e4832ba3e3dbd1c77b52d8f1f0a8"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72ce13a647081d7bbd2d887ff33be930a4", "guid": "7eee3bf7631ebb48b327684c83e1cb7255b3f0a5f11d6de18b884d9609576302"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb726bf8a1fb5b14f7ecbcbdd774cdda6362", "guid": "7eee3bf7631ebb48b327684c83e1cb72dcf1b5c0270beb92167e80ff888c6488"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb7243db8ce44a03bb25b641b18320b45c52", "guid": "7eee3bf7631ebb48b327684c83e1cb72f8f969c806bf6770922b8eb3f971a828"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb721668f5a961a50f28dfc50a038b923247", "guid": "7eee3bf7631ebb48b327684c83e1cb721c1035f96da8d81a09976df146a7042f"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb72a59f9b322edd85cac430cf9078750c53", "guid": "7eee3bf7631ebb48b327684c83e1cb722cde82120a749f6c921adc05b2395a8f"}, {"fileReference": "7eee3bf7631ebb48b327684c83e1cb725ab7fc32ac9d2882d5c9452af342409a", "guid": "7eee3bf7631ebb48b327684c83e1cb72203360da63d9ddbad14cc0a4e7107331"}], "guid": "7eee3bf7631ebb48b327684c83e1cb72913658b10f34845f60b3d63bfb4758ed", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "7eee3bf7631ebb48b327684c83e1cb72a223eeeeda09d692f4f53d34e47b4051", "name": "SuperSokoban", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "7eee3bf7631ebb48b327684c83e1cb72cfb80ea1488e8f9c445b6a0b86355e50", "name": "SuperSokoban.app", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.application", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "$(PRODUCT_BUNDLE_IDENTIFIER)", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}