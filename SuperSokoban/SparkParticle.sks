<skspritenode version="1.0">
    <emitter>
        <attribute name="particleBirthRate" type="float" value="150"/>
        <attribute name="particleLifetime" type="float" value="1"/>
        <attribute name="particleLifetimeRange" type="float" value="0.5"/>
        <attribute name="particlePositionRangeX" type="float" value="100"/>
        <attribute name="particlePositionRangeY" type="float" value="100"/>
        <attribute name="particleSpeed" type="float" value="200"/>
        <attribute name="particleSpeedRange" type="float" value="100"/>
        <attribute name="particleRotation" type="float" value="0"/>
        <attribute name="particleRotationRange" type="float" value="0"/>
        <attribute name="particleRotationSpeed" type="float" value="0"/>
        <attribute name="particleColorBlendFactor" type="float" value="1"/>
        <attribute name="particleColorBlendFactorRange" type="float" value="0"/>
        <attribute name="particleColorBlendFactorSpeed" type="float" value="0"/>
        <attribute name="particleColorRed" type="float" value="1"/>
        <attribute name="particleColorGreen" type="float" value="0.8"/>
        <attribute name="particleColorBlue" type="float" value="0.2"/>
        <attribute name="particleColorAlpha" type="float" value="1"/>
        <attribute name="particleColorSequence">
            <keyframes>
                <keyframe time="0.0" red="1" green="0.9" blue="0.3" alpha="1"/>
                <keyframe time="0.5" red="1" green="0.5" blue="0" alpha="0.75"/>
                <keyframe time="1.0" red="0.8" green="0.2" blue="0" alpha="0"/>
            </keyframes>
        </attribute>
        <attribute name="particleAlphaSequence">
            <keyframes>
                <keyframe time="0.0" value="1"/>
                <keyframe time="0.75" value="1"/>
                <keyframe time="1.0" value="0"/>
            </keyframes>
        </attribute>
        <attribute name="particleScaleSequence">
            <keyframes>
                <keyframe time="0.0" scaleX="0.2" scaleY="0.2"/>
                <keyframe time="0.25" scaleX="0.3" scaleY="0.3"/>
                <keyframe time="1.0" scaleX="0.1" scaleY="0.1"/>
            </keyframes>
        </attribute>
        <attribute name="particleTexture" type="string" value="spark.png"/>
        <attribute name="emissionAngle" type="float" value="0"/>
        <attribute name="emissionAngleRange" type="float" value="360"/>
        <attribute name="blendMode" type="string" value="add"/>
    </emitter>
</skspritenode>
<!-- This is a placeholder for SparkParticle.sks. 
     You would typically create this using Xcode's SpriteKit Particle File editor. 
     This XML structure is a simplified representation. 
     You'll also need a 'spark.png' image (a small white or yellowish dot/star shape) in your project for the texture. -->