//
//  LevelGeneratorTest.swift
//  SuperSokoban
//
//  测试LevelGenerator的快速生成功能
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation

/// 测试LevelGenerator的生成速度和质量
class LevelGeneratorTest {
    
    static func runQuickTest() {
        print("[LevelGeneratorTest] 🧪 开始快速测试LevelGenerator...")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 创建LevelGenerator实例
        let generator = LevelGenerator()
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let totalTime = endTime - startTime
        
        print("[LevelGeneratorTest] ⏱️ 生成时间: \(String(format: "%.3f", totalTime))秒")
        print("[LevelGeneratorTest] 📊 生成关卡数: \(generator.numberOfLevels)")
        
        // 测试前几关
        testFirstFewLevels(generator: generator)
        
        // 测试不同难度的关卡
        testDifferentDifficulties(generator: generator)
        
        print("[LevelGeneratorTest] ✅ 测试完成!")
    }
    
    private static func testFirstFewLevels(generator: LevelGenerator) {
        print("[LevelGeneratorTest] 🔍 测试前5关...")
        
        for i in 0..<min(5, generator.numberOfLevels) {
            if let level = generator.getLevel(at: i) {
                let validation = validateLevelBasic(level, levelNumber: i + 1)
                print("[LevelGeneratorTest] 关卡\(i + 1): \(validation ? "✅" : "❌")")
                
                if i == 0 {
                    printLevelLayout(level, levelNumber: i + 1)
                }
            }
        }
    }
    
    private static func testDifferentDifficulties(generator: LevelGenerator) {
        print("[LevelGeneratorTest] 🎯 测试不同难度关卡...")
        
        let testLevels = [1, 25, 50, 75, 100, 125, 150, 175, 200]
        
        for levelNum in testLevels {
            if levelNum <= generator.numberOfLevels {
                if let level = generator.getLevel(at: levelNum - 1) {
                    let validation = validateLevelBasic(level, levelNumber: levelNum)
                    let difficulty = getDifficultyName(for: levelNum)
                    print("[LevelGeneratorTest] 关卡\(levelNum) (\(difficulty)): \(validation ? "✅" : "❌")")
                }
            }
        }
    }
    
    private static func validateLevelBasic(_ level: [[Character]], levelNumber: Int) -> Bool {
        let size = level.count
        guard size >= 10 else {
            print("  ❌ 关卡\(levelNumber)太小: \(size)x\(size) (需要至少10x10)")
            return false
        }

        var playerCount = 0
        var boxCount = 0
        var targetCount = 0
        var wallCount = 0

        for row in level {
            guard row.count == size else {
                print("  ❌ 关卡\(levelNumber)不是正方形")
                return false
            }

            for cell in row {
                switch cell {
                case "P": playerCount += 1
                case "B": boxCount += 1
                case "T": targetCount += 1
                case "W": wallCount += 1
                default: break
                }
            }
        }

        // 复杂关卡验证：
        // 1. 有一个玩家
        // 2. 箱子和目标数量相等且至少3个（复杂性要求）
        // 3. 有足够的墙壁形成复杂结构
        let isValid = playerCount == 1 &&
                     boxCount == targetCount &&
                     boxCount >= 3 &&
                     wallCount >= size * 2  // 至少有足够的墙壁

        if !isValid {
            print("  ❌ 关卡\(levelNumber)验证失败: 玩家=\(playerCount), 箱子=\(boxCount), 目标=\(targetCount), 墙壁=\(wallCount)")
        } else {
            print("  ✅ 关卡\(levelNumber)复杂度验证通过: \(size)x\(size), \(boxCount)个箱子, \(wallCount)个墙壁")
        }

        return isValid
    }
    
    private static func printLevelLayout(_ level: [[Character]], levelNumber: Int) {
        print("[LevelGeneratorTest] 📋 关卡\(levelNumber)布局:")
        for row in level {
            let rowString = row.map { char in
                switch char {
                case "W": return "🧱"
                case "G": return "⬜"
                case "P": return "🧑"
                case "B": return "📦"
                case "T": return "🎯"
                default: return "❓"
                }
            }.joined()
            print("  \(rowString)")
        }
    }
    
    private static func getDifficultyName(for levelNumber: Int) -> String {
        switch levelNumber {
        case 1...50: return "复杂挑战"
        case 51...100: return "高级复杂"
        case 101...150: return "专家复杂"
        case 151...200: return "大师复杂"
        default: return "超级复杂"
        }
    }
}
