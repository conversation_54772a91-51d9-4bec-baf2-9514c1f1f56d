//
//  ScreenAdaptationManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit
import UIKit

/// 屏幕适配管理器 - 负责所有尺寸相关的计算和适配
class ScreenAdaptationManager {
    
    // MARK: - Properties
    private weak var gameScene: GameScene?
    
    /// 屏幕尺寸信息
    struct ScreenInfo {
        let screenSize: CGSize
        let safeAreaInsets: UIEdgeInsets
        let availableGameArea: CGSize
        let deviceType: DeviceType
        let scaleFactor: CGFloat
    }
    
    /// 设备类型
    enum DeviceType {
        case iPhone_SE      // 小屏iPhone (4.7英寸及以下)
        case iPhone_Regular // 标准iPhone (6.1英寸)
        case iPhone_Plus    // 大屏iPhone (6.7英寸及以上)
        case iPad_Mini      // iPad Mini
        case iPad_Regular   // 标准iPad
        case iPad_Pro       // iPad Pro
    }
    
    /// 适配后的尺寸信息
    struct AdaptedSizes {
        let tileSize: CGFloat
        let playerSize: CGFloat
        let boxSize: CGFloat
        let targetSize: CGFloat
        let wallSize: CGFloat
        let groundSize: CGFloat
        let uiElementScale: CGFloat
        let fontScale: CGFloat
    }
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
    }
    
    // MARK: - Public Methods
    
    /// 获取当前屏幕信息
    func getCurrentScreenInfo() -> ScreenInfo {
        guard let scene = gameScene else {
            return ScreenInfo(
                screenSize: CGSize(width: 375, height: 667),
                safeAreaInsets: .zero,
                availableGameArea: CGSize(width: 375, height: 567),
                deviceType: .iPhone_Regular,
                scaleFactor: 1.0
            )
        }
        
        let screenSize = scene.size
        let safeAreaInsets = scene.safeAreaInsets
        
        // 计算可用游戏区域（减去安全区域和UI预留空间）
        let availableWidth = screenSize.width - safeAreaInsets.left - safeAreaInsets.right - 40 // 左右各留20点边距
        let availableHeight = screenSize.height - safeAreaInsets.top - safeAreaInsets.bottom - 200 // 上下留给UI元素
        let availableGameArea = CGSize(width: availableWidth, height: availableHeight)
        
        let deviceType = determineDeviceType(screenSize: screenSize)
        let scaleFactor = calculateScaleFactor(deviceType: deviceType)
        
        return ScreenInfo(
            screenSize: screenSize,
            safeAreaInsets: safeAreaInsets,
            availableGameArea: availableGameArea,
            deviceType: deviceType,
            scaleFactor: scaleFactor
        )
    }
    
    /// 根据关卡数据计算适配后的尺寸
    /// - Parameters:
    ///   - levelData: 关卡数据
    ///   - levelIndex: 关卡索引
    /// - Returns: 适配后的尺寸信息
    func calculateAdaptedSizes(for levelData: [[Character]], levelIndex: Int) -> AdaptedSizes {
        let screenInfo = getCurrentScreenInfo()
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0
        
        // 计算基础瓦片大小
        let baseTileSize = calculateBaseTileSize(
            levelWidth: levelWidth,
            levelHeight: levelHeight,
            availableArea: screenInfo.availableGameArea,
            deviceType: screenInfo.deviceType,
            levelIndex: levelIndex
        )
        
        // 根据设备类型和瓦片大小计算其他元素尺寸
        let adaptedSizes = AdaptedSizes(
            tileSize: baseTileSize,
            playerSize: baseTileSize * getPlayerSizeRatio(deviceType: screenInfo.deviceType),
            boxSize: baseTileSize * getBoxSizeRatio(deviceType: screenInfo.deviceType),
            targetSize: baseTileSize * getTargetSizeRatio(deviceType: screenInfo.deviceType),
            wallSize: baseTileSize,
            groundSize: baseTileSize,
            uiElementScale: screenInfo.scaleFactor,
            fontScale: calculateFontScale(deviceType: screenInfo.deviceType, tileSize: baseTileSize)
        )
        
        print("[ScreenAdaptationManager] Calculated sizes for \(screenInfo.deviceType):")
        print("  - Tile: \(adaptedSizes.tileSize)")
        print("  - Player: \(adaptedSizes.playerSize)")
        print("  - Box: \(adaptedSizes.boxSize)")
        print("  - UI Scale: \(adaptedSizes.uiElementScale)")
        
        return adaptedSizes
    }
    
    /// 获取游戏区域的起始位置（居中）
    /// - Parameters:
    ///   - levelWidth: 关卡宽度
    ///   - levelHeight: 关卡高度
    ///   - tileSize: 瓦片大小
    /// - Returns: 起始位置
    func getGameAreaStartPosition(levelWidth: Int, levelHeight: Int, tileSize: CGFloat) -> CGPoint {
        guard let scene = gameScene else { return .zero }
        
        let totalLevelWidth = CGFloat(levelWidth) * tileSize
        let totalLevelHeight = CGFloat(levelHeight) * tileSize
        
        let startX = (scene.size.width - totalLevelWidth) / 2 + tileSize / 2
        let startY = (scene.size.height - totalLevelHeight) / 2 + tileSize / 2 + 60 // 向上偏移给UI留空间
        
        return CGPoint(x: startX, y: startY)
    }
    
    // MARK: - Private Methods
    
    /// 确定设备类型
    private func determineDeviceType(screenSize: CGSize) -> DeviceType {
        let maxDimension = max(screenSize.width, screenSize.height)
        let minDimension = min(screenSize.width, screenSize.height)
        
        // 根据屏幕尺寸判断设备类型
        if maxDimension >= 1366 { // iPad Pro 12.9"
            return .iPad_Pro
        } else if maxDimension >= 1194 { // iPad Pro 11", iPad Air
            return .iPad_Regular
        } else if maxDimension >= 1080 { // iPad Mini
            return .iPad_Mini
        } else if maxDimension >= 926 { // iPhone 14 Pro Max, iPhone 15 Plus等
            return .iPhone_Plus
        } else if maxDimension >= 844 { // iPhone 14, iPhone 15等
            return .iPhone_Regular
        } else { // iPhone SE, iPhone 8等
            return .iPhone_SE
        }
    }
    
    /// 计算缩放因子
    private func calculateScaleFactor(deviceType: DeviceType) -> CGFloat {
        switch deviceType {
        case .iPhone_SE:
            return 0.8
        case .iPhone_Regular:
            return 1.0
        case .iPhone_Plus:
            return 1.2
        case .iPad_Mini:
            return 1.4
        case .iPad_Regular:
            return 1.6
        case .iPad_Pro:
            return 2.0
        }
    }
    
    /// 计算基础瓦片大小
    private func calculateBaseTileSize(
        levelWidth: Int,
        levelHeight: Int,
        availableArea: CGSize,
        deviceType: DeviceType,
        levelIndex: Int
    ) -> CGFloat {
        
        // 根据关卡大小计算理想瓦片大小
        let maxTileSizeByWidth = availableArea.width / CGFloat(levelWidth)
        let maxTileSizeByHeight = availableArea.height / CGFloat(levelHeight)
        let idealTileSize = min(maxTileSizeByWidth, maxTileSizeByHeight)
        
        // 根据设备类型设置尺寸范围
        let (minSize, maxSize) = getTileSizeRange(deviceType: deviceType)
        
        // 教学关卡使用相对固定的大小
        if levelIndex < 5 {
            let tutorialSize = getTutorialTileSize(deviceType: deviceType)
            return max(min(tutorialSize, maxSize), minSize)
        }
        
        // 动态关卡使用计算出的理想大小
        return max(min(idealTileSize, maxSize), minSize)
    }
    
    /// 获取瓦片大小范围
    private func getTileSizeRange(deviceType: DeviceType) -> (min: CGFloat, max: CGFloat) {
        switch deviceType {
        case .iPhone_SE:
            return (min: 20, max: 35)
        case .iPhone_Regular:
            return (min: 25, max: 45)
        case .iPhone_Plus:
            return (min: 30, max: 55)
        case .iPad_Mini:
            return (min: 35, max: 65)
        case .iPad_Regular:
            return (min: 40, max: 75)
        case .iPad_Pro:
            return (min: 50, max: 90)
        }
    }
    
    /// 获取教学关卡瓦片大小
    private func getTutorialTileSize(deviceType: DeviceType) -> CGFloat {
        switch deviceType {
        case .iPhone_SE:
            return 28
        case .iPhone_Regular:
            return 35
        case .iPhone_Plus:
            return 42
        case .iPad_Mini:
            return 50
        case .iPad_Regular:
            return 60
        case .iPad_Pro:
            return 70
        }
    }
    
    /// 获取玩家大小比例
    private func getPlayerSizeRatio(deviceType: DeviceType) -> CGFloat {
        switch deviceType {
        case .iPhone_SE, .iPhone_Regular:
            return 0.82 // 增大玩家尺寸，提高可见性
        case .iPhone_Plus:
            return 0.85
        case .iPad_Mini, .iPad_Regular, .iPad_Pro:
            return 0.88 // iPad上可以稍大一些
        }
    }

    /// 获取箱子大小比例
    private func getBoxSizeRatio(deviceType: DeviceType) -> CGFloat {
        switch deviceType {
        case .iPhone_SE, .iPhone_Regular:
            return 0.92 // 增大箱子尺寸，更加醒目
        case .iPhone_Plus:
            return 0.94
        case .iPad_Mini, .iPad_Regular, .iPad_Pro:
            return 0.95
        }
    }
    
    /// 获取目标点大小比例
    private func getTargetSizeRatio(deviceType: DeviceType) -> CGFloat {
        return 1.0 // 目标点始终与瓦片同大小
    }
    
    /// 计算字体缩放比例
    private func calculateFontScale(deviceType: DeviceType, tileSize: CGFloat) -> CGFloat {
        let baseScale = calculateScaleFactor(deviceType: deviceType)
        let tileSizeScale = tileSize / 40.0 // 以40为基准大小
        return baseScale * tileSizeScale
    }
    
    /// 获取适配后的字体大小
    /// - Parameters:
    ///   - baseFontSize: 基础字体大小
    ///   - fontScale: 字体缩放比例
    /// - Returns: 适配后的字体大小
    func getAdaptedFontSize(baseFontSize: CGFloat, fontScale: CGFloat) -> CGFloat {
        return baseFontSize * fontScale
    }
    
    /// 获取适配后的UI元素大小
    /// - Parameters:
    ///   - baseSize: 基础大小
    ///   - uiScale: UI缩放比例
    /// - Returns: 适配后的大小
    func getAdaptedUISize(baseSize: CGSize, uiScale: CGFloat) -> CGSize {
        return CGSize(width: baseSize.width * uiScale, height: baseSize.height * uiScale)
    }
}
