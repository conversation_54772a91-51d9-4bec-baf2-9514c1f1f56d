//
//  ViewController.swift
//  SuperSokoban
//
//  Created by 斗斗 on 2025/5/30.
//

import UIKit
import SpriteKit
import GameplayKit

class ViewController: UIViewController {

    override func viewDidLoad() {
        super.viewDidLoad()

        // VISUAL ENHANCEMENT: Professional gradient background
        setupGradientBackground()

        // 创建SKView并添加到主视图
        let skView = SKView()
        skView.translatesAutoresizingMaskIntoConstraints = false
        self.view.addSubview(skView)

        // 设置约束，适配安全区域
        NSLayoutConstraint.activate([
            skView.topAnchor.constraint(equalTo: self.view.safeAreaLayoutGuide.topAnchor),
            skView.leadingAnchor.constraint(equalTo: self.view.safeAreaLayoutGuide.leadingAnchor),
            skView.trailingAnchor.constraint(equalTo: self.view.safeAreaLayoutGuide.trailingAnchor),
            skView.bottomAnchor.constraint(equalTo: self.view.safeAreaLayoutGuide.bottomAnchor)
        ])

        // 等待视图布局完成后再创建场景
        DispatchQueue.main.async {
            // 创建GameScene实例
            print("[ViewController] skView.bounds.size: \(skView.bounds.size)")
            let scene = GameScene(size: skView.bounds.size)

            // 传递安全区域信息给场景
            scene.safeAreaInsets = self.view.safeAreaInsets

            // 设置场景的缩放模式以适应视图
            scene.scaleMode = .aspectFill

            // 呈现场景
            print("[ViewController] About to present scene...")
            skView.presentScene(scene)
            print("[ViewController] Scene presented.")

            // 可选：优化渲染性能
            skView.ignoresSiblingOrder = true

            // 生产环境不显示调试信息
            #if DEBUG
            skView.showsFPS = true
            skView.showsNodeCount = true
            #endif
        }
    }

    // 隐藏状态栏
    override var prefersStatusBarHidden: Bool {
        return true
    }

    // 支持的屏幕方向（如果需要限制）
    override var supportedInterfaceOrientations: UIInterfaceOrientationMask {
        if UIDevice.current.userInterfaceIdiom == .phone {
            return .allButUpsideDown
        } else {
            return .all
        }
    }

    /// VISUAL ENHANCEMENT: Setup professional gradient background
    private func setupGradientBackground() {
        // Create gradient layer with professional color scheme
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = view.bounds

        // Professional dark blue to purple gradient
        gradientLayer.colors = [
            UIColor(red: 0.1, green: 0.2, blue: 0.4, alpha: 1.0).cgColor,  // Dark blue
            UIColor(red: 0.2, green: 0.1, blue: 0.3, alpha: 1.0).cgColor,  // Dark purple
            UIColor(red: 0.05, green: 0.05, blue: 0.2, alpha: 1.0).cgColor // Very dark blue
        ]

        // Diagonal gradient for more visual interest
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)

        // Add subtle animation to the gradient
        let animation = CABasicAnimation(keyPath: "colors")
        animation.duration = 8.0
        animation.repeatCount = .infinity
        animation.autoreverses = true

        let alternateColors = [
            UIColor(red: 0.15, green: 0.25, blue: 0.45, alpha: 1.0).cgColor,
            UIColor(red: 0.25, green: 0.15, blue: 0.35, alpha: 1.0).cgColor,
            UIColor(red: 0.1, green: 0.1, blue: 0.25, alpha: 1.0).cgColor
        ]

        animation.fromValue = gradientLayer.colors
        animation.toValue = alternateColors
        gradientLayer.add(animation, forKey: "colorAnimation")

        // Insert gradient as background
        view.layer.insertSublayer(gradientLayer, at: 0)

        // Update gradient frame when view bounds change
        DispatchQueue.main.async {
            gradientLayer.frame = self.view.bounds
        }
    }


}

