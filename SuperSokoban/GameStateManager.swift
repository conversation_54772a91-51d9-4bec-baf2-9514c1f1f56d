//
//  GameStateManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// 游戏状态数据结构
struct GameState {
    let playerPosition: CGPoint
    let boxPositions: [CGPoint]
    let moveCount: Int
    let timestamp: Date
    
    init(playerPosition: CGPoint, boxPositions: [CGPoint], moveCount: Int = 0) {
        self.playerPosition = playerPosition
        self.boxPositions = boxPositions
        self.moveCount = moveCount
        self.timestamp = Date()
    }
}

/// 游戏状态管理器 - 负责状态保存、回退和重置
class GameStateManager {
    
    // MARK: - Properties
    private var moveHistory: [GameState] = []
    private var initialState: GameState?
    private let maxHistoryCount = 50
    
    // MARK: - Public Methods
    
    /// 保存初始状态
    func saveInitialState(playerPosition: CGPoint, boxPositions: [CGPoint]) {
        let state = GameState(playerPosition: playerPosition, boxPositions: boxPositions)
        initialState = state
        moveHistory.removeAll()
        print("[GameStateManager] 初始状态已保存 - 玩家: \(playerPosition), 箱子数量: \(boxPositions.count)")
    }
    
    /// 保存当前移动状态（在移动前调用）
    func saveMoveState(playerPosition: CGPoint, boxPositions: [CGPoint]) {
        let state = GameState(
            playerPosition: playerPosition, 
            boxPositions: boxPositions,
            moveCount: moveHistory.count
        )
        
        moveHistory.append(state)
        
        // 限制历史记录数量
        if moveHistory.count > maxHistoryCount {
            moveHistory.removeFirst()
        }
        
        print("[GameStateManager] 移动状态已保存 - 历史记录数: \(moveHistory.count)")
    }
    
    /// 回退到上一个状态
    func undoLastMove() -> GameState? {
        guard !moveHistory.isEmpty else {
            print("[GameStateManager] 没有可回退的移动")
            return nil
        }
        
        // 移除当前状态
        moveHistory.removeLast()
        
        // 返回上一个状态，如果没有则返回初始状态
        if let lastState = moveHistory.last {
            print("[GameStateManager] 回退到历史状态 - 移动数: \(lastState.moveCount)")
            return lastState
        } else if let initial = initialState {
            print("[GameStateManager] 回退到初始状态")
            return initial
        }
        
        return nil
    }
    
    /// 重置到初始状态
    func resetToInitialState() -> GameState? {
        guard let initial = initialState else {
            print("[GameStateManager] 错误: 没有保存的初始状态")
            return nil
        }
        
        moveHistory.removeAll()
        print("[GameStateManager] 已重置到初始状态")
        return initial
    }
    
    /// 获取当前移动数
    func getCurrentMoveCount() -> Int {
        return moveHistory.count
    }
    
    /// 检查是否有可回退的移动
    func canUndo() -> Bool {
        return !moveHistory.isEmpty
    }
    
    /// 获取历史记录统计
    func getHistoryStats() -> (count: Int, maxCount: Int) {
        return (moveHistory.count, maxHistoryCount)
    }
    
    /// 清空所有状态
    func clearAllStates() {
        moveHistory.removeAll()
        initialState = nil
        print("[GameStateManager] 所有状态已清空")
    }
}
