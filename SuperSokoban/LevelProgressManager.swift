//
//  LevelProgressManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation

/// 关卡进度数据结构
struct LevelProgress: Codable {
    let levelIndex: Int
    let isCompleted: Bool
    let bestMoveCount: Int?
    let completionTime: Date?
    let attempts: Int
    
    init(levelIndex: Int, isCompleted: Bool = false, bestMoveCount: Int? = nil, attempts: Int = 0) {
        self.levelIndex = levelIndex
        self.isCompleted = isCompleted
        self.bestMoveCount = bestMoveCount
        self.completionTime = isCompleted ? Date() : nil
        self.attempts = attempts
    }
}

/// 关卡进度管理器 - 负责保存和加载用户的关卡进度
class LevelProgressManager {
    
    // MARK: - Properties
    private let userDefaults = UserDefaults.standard
    private let progressKey = "SuperSokoban_LevelProgress"
    private var levelProgress: [Int: LevelProgress] = [:]
    
    // MARK: - Initialization
    init() {
        loadProgress()
    }
    
    // MARK: - Public Methods
    
    /// 标记关卡为已完成
    /// - Parameters:
    ///   - levelIndex: 关卡索引
    ///   - moveCount: 完成时的移动步数
    func markLevelCompleted(levelIndex: Int, moveCount: Int) {
        let currentProgress = levelProgress[levelIndex]
        let attempts = (currentProgress?.attempts ?? 0) + 1
        
        // 更新最佳记录
        let bestMoveCount: Int
        if let existingBest = currentProgress?.bestMoveCount {
            bestMoveCount = min(existingBest, moveCount)
        } else {
            bestMoveCount = moveCount
        }
        
        let progress = LevelProgress(
            levelIndex: levelIndex,
            isCompleted: true,
            bestMoveCount: bestMoveCount,
            attempts: attempts
        )
        
        levelProgress[levelIndex] = progress
        saveProgress()
        
        print("[LevelProgressManager] 关卡\(levelIndex + 1)已完成 - 移动步数: \(moveCount), 最佳记录: \(bestMoveCount), 尝试次数: \(attempts)")
    }
    
    /// 增加关卡尝试次数
    /// - Parameter levelIndex: 关卡索引
    func incrementAttempts(levelIndex: Int) {
        let currentProgress = levelProgress[levelIndex]
        let attempts = (currentProgress?.attempts ?? 0) + 1
        
        let progress = LevelProgress(
            levelIndex: levelIndex,
            isCompleted: currentProgress?.isCompleted ?? false,
            bestMoveCount: currentProgress?.bestMoveCount,
            attempts: attempts
        )
        
        levelProgress[levelIndex] = progress
        saveProgress()
    }
    
    /// 检查关卡是否已完成
    /// - Parameter levelIndex: 关卡索引
    /// - Returns: 是否已完成
    func isLevelCompleted(levelIndex: Int) -> Bool {
        return levelProgress[levelIndex]?.isCompleted ?? false
    }
    
    /// 获取关卡的最佳移动步数
    /// - Parameter levelIndex: 关卡索引
    /// - Returns: 最佳移动步数，如果未完成则返回nil
    func getBestMoveCount(levelIndex: Int) -> Int? {
        return levelProgress[levelIndex]?.bestMoveCount
    }
    
    /// 获取关卡尝试次数
    /// - Parameter levelIndex: 关卡索引
    /// - Returns: 尝试次数
    func getAttempts(levelIndex: Int) -> Int {
        return levelProgress[levelIndex]?.attempts ?? 0
    }
    
    /// 获取已完成的关卡数量
    /// - Returns: 已完成的关卡数量
    func getCompletedLevelCount() -> Int {
        return levelProgress.values.filter { $0.isCompleted }.count
    }
    
    /// 获取最高已完成的关卡索引
    /// - Returns: 最高已完成的关卡索引，如果没有完成任何关卡则返回-1
    func getHighestCompletedLevel() -> Int {
        let completedLevels = levelProgress.values.filter { $0.isCompleted }
        return completedLevels.map { $0.levelIndex }.max() ?? -1
    }
    
    /// 检查关卡是否可以游玩（已解锁）
    /// - Parameter levelIndex: 关卡索引
    /// - Returns: 是否可以游玩
    func isLevelUnlocked(levelIndex: Int) -> Bool {
        // 第一关总是解锁的
        if levelIndex == 0 {
            return true
        }
        
        // 前5关（教学关卡）按顺序解锁
        if levelIndex < 5 {
            return isLevelCompleted(levelIndex: levelIndex - 1)
        }
        
        // 动态生成的关卡：完成前5关后全部解锁
        return getHighestCompletedLevel() >= 4
    }
    
    /// 获取进度统计信息
    /// - Returns: 进度统计字符串
    func getProgressSummary() -> String {
        let completedCount = getCompletedLevelCount()
        let highestLevel = getHighestCompletedLevel()
        let totalAttempts = levelProgress.values.reduce(0) { $0 + $1.attempts }
        
        return """
        已完成关卡: \(completedCount)
        最高关卡: \(highestLevel + 1)
        总尝试次数: \(totalAttempts)
        """
    }
    
    /// 重置所有进度
    func resetAllProgress() {
        levelProgress.removeAll()
        userDefaults.removeObject(forKey: progressKey)
        print("[LevelProgressManager] 所有进度已重置")
    }
    
    // MARK: - Private Methods
    
    /// 保存进度到本地
    private func saveProgress() {
        do {
            let data = try JSONEncoder().encode(Array(levelProgress.values))
            userDefaults.set(data, forKey: progressKey)
            print("[LevelProgressManager] 进度已保存到本地")
        } catch {
            print("[LevelProgressManager] 保存进度失败: \(error)")
        }
    }
    
    /// 从本地加载进度
    private func loadProgress() {
        guard let data = userDefaults.data(forKey: progressKey) else {
            print("[LevelProgressManager] 没有找到保存的进度数据")
            return
        }
        
        do {
            let progressArray = try JSONDecoder().decode([LevelProgress].self, from: data)
            levelProgress = Dictionary(uniqueKeysWithValues: progressArray.map { ($0.levelIndex, $0) })
            print("[LevelProgressManager] 已加载\(levelProgress.count)个关卡的进度数据")
        } catch {
            print("[LevelProgressManager] 加载进度失败: \(error)")
        }
    }
}
