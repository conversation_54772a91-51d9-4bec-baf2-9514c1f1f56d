//
//  LevelLoadingManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// 关卡加载管理器 - 负责关卡数据的加载和场景设置
class LevelLoadingManager {
    
    // MARK: - Properties
    private weak var gameScene: GameScene?
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
    }
    
    // MARK: - Public Methods
    
    /// 加载指定索引的关卡
    /// - Parameter index: 关卡索引
    func loadLevel(index: Int) {
        guard let scene = gameScene else { return }
        
        print("[LevelLoadingManager] Attempting to load level at index: \(index)")
        
        // 根据用户要求：前5关是写死的教学关卡，其他关卡使用动态生成
        let levelData = getLevelData(for: index)
        
        if let levelData = levelData {
            scene.levelData = levelData
            scene.currentLevelIndex = index
            
            // 增加关卡尝试次数
            scene.progressManager.incrementAttempts(levelIndex: index)
            
            // 根据关卡调整瓦片大小
            adjustTileSizeForLevel(index: index)
            
            print("[LevelLoadingManager] Level data loaded successfully for index \(index)")
            setupLevel(with: levelData)
            
            // 保存初始状态到管理器
            saveInitialStateToManager()
        } else {
            print("[LevelLoadingManager] 无法加载关卡索引: \(index)。可能是最后一个关卡了。")
            showGameCompletedScreen()
        }
    }
    
    /// 设置关卡场景
    /// - Parameter levelData: 关卡数据
    func setupLevel(with levelData: [[Character]]) {
        guard let scene = gameScene else { return }
        
        print("[LevelLoadingManager] Setting up level with current data...")
        
        // 清理旧的节点
        cleanupOldLevel()
        
        // 重新设置统一的渐变背景
        scene.setupGradientBackground()
        
        // 使用UIManager设置所有UI元素
        scene.uiManager.setupAllUIElements(safeAreaInsets: scene.safeAreaInsets, currentLevel: scene.currentLevelIndex)
        
        // 创建游戏元素
        createGameElements(with: levelData)
        
        print("[LevelLoadingManager] Level setup completed")
    }
    
    // MARK: - Private Methods
    
    /// 获取关卡数据
    /// - Parameter index: 关卡索引
    /// - Returns: 关卡数据
    private func getLevelData(for index: Int) -> [[Character]]? {
        guard let scene = gameScene else { return nil }
        
        if index < 5 {
            // 前5关：使用写死的教学关卡
            let levelData = scene.levelManager.getLevel(at: index)
            print("[LevelLoadingManager] Loading tutorial level from LevelManager: level \(index + 1)/5 (教学关卡)")
            return levelData
        } else {
            // 第6关及以后：使用动态生成的关卡
            let generatorIndex = index - 5 // 动态生成从第6关开始，对应generator的第0关
            if generatorIndex < scene.levelGenerator.numberOfLevels {
                let levelData = scene.levelGenerator.getLevel(at: generatorIndex)
                print("[LevelLoadingManager] Loading dynamic level from LevelGenerator: level \(index + 1) (动态生成第\(generatorIndex + 1)关)")
                return levelData
            } else {
                // 如果超出了动态生成的范围，循环使用动态关卡
                let cycleIndex = generatorIndex % scene.levelGenerator.numberOfLevels
                let levelData = scene.levelGenerator.getLevel(at: cycleIndex)
                print("[LevelLoadingManager] Loading cycled dynamic level: level \(index + 1) (循环第\(cycleIndex + 1)关)")
                return levelData
            }
        }
    }
    
    /// 调整瓦片大小
    /// - Parameter index: 关卡索引
    private func adjustTileSizeForLevel(index: Int) {
        guard let scene = gameScene else { return }
        
        // 根据关卡类型调整瓦片大小
        if index < 5 {
            // 教学关卡使用固定大小
            scene.tileSize = 40.0
        } else {
            // 动态关卡根据网格大小调整
            let levelHeight = scene.levelData.count
            let levelWidth = scene.levelData.first?.count ?? 0
            let maxDimension = max(levelHeight, levelWidth)
            
            // 动态计算瓦片大小，确保关卡适合屏幕
            let availableHeight = scene.size.height - scene.safeAreaInsets.top - scene.safeAreaInsets.bottom - 200
            let availableWidth = scene.size.width - 100
            
            let maxTileSizeByHeight = availableHeight / CGFloat(maxDimension)
            let maxTileSizeByWidth = availableWidth / CGFloat(maxDimension)
            
            scene.tileSize = min(maxTileSizeByHeight, maxTileSizeByWidth, 50.0) // 最大不超过50
            scene.tileSize = max(scene.tileSize, 25.0) // 最小不小于25
        }
        
        print("[LevelLoadingManager] Adjusted tile size to: \(scene.tileSize)")
    }
    
    /// 清理旧关卡
    private func cleanupOldLevel() {
        guard let scene = gameScene else { return }
        
        // 清除所有旧节点，包括UI元素
        scene.removeAllChildren()
        scene.boxes.removeAll()
        scene.walls.removeAll()
        scene.targets.removeAll()
        scene.groundTiles.removeAll()
        scene.player = nil
    }
    
    /// 创建游戏元素
    /// - Parameter levelData: 关卡数据
    private func createGameElements(with levelData: [[Character]]) {
        guard let scene = gameScene else { return }
        
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0
        
        // 计算起始位置，使关卡居中显示
        let totalLevelWidth = CGFloat(levelWidth) * scene.tileSize
        let totalLevelHeight = CGFloat(levelHeight) * scene.tileSize
        let startX = (scene.size.width - totalLevelWidth) / 2 + scene.tileSize / 2
        let startY = (scene.size.height - totalLevelHeight) / 2 + scene.tileSize / 2 + 50 // 向上偏移50给UI留空间
        
        // 遍历关卡数据，创建对应的游戏元素
        for (rowIndex, row) in levelData.enumerated() {
            for (colIndex, cell) in row.enumerated() {
                let x = startX + CGFloat(colIndex) * scene.tileSize
                let y = startY + CGFloat(levelHeight - 1 - rowIndex) * scene.tileSize // 翻转Y轴
                let position = CGPoint(x: x, y: y)
                
                // 首先创建地面瓦片（除了墙壁）
                if cell != "W" {
                    let groundTile = createGroundTile(at: position)
                    scene.addChild(groundTile)
                    scene.groundTiles.append(groundTile)
                }
                
                // 根据字符创建对应的游戏元素
                switch cell {
                case "W":
                    let wall = createWall(at: position)
                    scene.addChild(wall)
                    scene.walls.append(wall)
                    
                case "P":
                    let player = createPlayer(at: position)
                    scene.addChild(player)
                    scene.player = player
                    
                case "B":
                    let box = createBox(at: position)
                    scene.addChild(box)
                    scene.boxes.append(box)
                    
                case "T":
                    let target = createTarget(at: position)
                    scene.addChild(target)
                    scene.targets.append(target)
                    
                default:
                    break // "G" 或其他字符，只有地面瓦片
                }
            }
        }
        
        print("[LevelLoadingManager] Created \(scene.walls.count) walls, \(scene.boxes.count) boxes, \(scene.targets.count) targets")
    }
    
    /// 创建地面瓦片
    private func createGroundTile(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene else { return SKSpriteNode() }
        
        let groundTile = SKSpriteNode(color: SKColor.lightGray.withAlphaComponent(0.3), size: CGSize(width: scene.tileSize, height: scene.tileSize))
        groundTile.position = position
        groundTile.zPosition = 1
        groundTile.name = "groundTile"
        
        return groundTile
    }
    
    /// 创建墙壁
    private func createWall(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene else { return SKSpriteNode() }
        
        let wall = SKSpriteNode(color: SKColor.brown, size: CGSize(width: scene.tileSize, height: scene.tileSize))
        wall.position = position
        wall.zPosition = 10
        wall.name = "wall"
        
        // 添加3D效果
        let highlight = SKSpriteNode(color: SKColor.white.withAlphaComponent(0.3), size: CGSize(width: scene.tileSize, height: 4))
        highlight.position = CGPoint(x: 0, y: scene.tileSize/2 - 2)
        highlight.zPosition = 1
        wall.addChild(highlight)
        
        let shadow = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.3), size: CGSize(width: scene.tileSize, height: 4))
        shadow.position = CGPoint(x: 0, y: -scene.tileSize/2 + 2)
        shadow.zPosition = 1
        wall.addChild(shadow)
        
        return wall
    }
    
    /// 创建玩家
    private func createPlayer(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene else { return SKSpriteNode() }
        
        let player = SKSpriteNode(color: SKColor.orange, size: CGSize(width: scene.tileSize * 0.8, height: scene.tileSize * 0.8))
        player.position = position
        player.zPosition = 20
        player.name = "player"
        
        // 创建圆形外观
        let circleTexture = createCircleTexture(size: CGSize(width: scene.tileSize * 0.8, height: scene.tileSize * 0.8), color: SKColor.orange)
        player.texture = circleTexture
        
        // 添加眼睛
        let leftEye = SKSpriteNode(color: SKColor.black, size: CGSize(width: 6, height: 6))
        leftEye.position = CGPoint(x: -8, y: 8)
        leftEye.zPosition = 1
        player.addChild(leftEye)
        
        let rightEye = SKSpriteNode(color: SKColor.black, size: CGSize(width: 6, height: 6))
        rightEye.position = CGPoint(x: 8, y: 8)
        rightEye.zPosition = 1
        player.addChild(rightEye)
        
        // 添加嘴巴
        let mouth = SKShapeNode(path: createSmilePath())
        mouth.strokeColor = SKColor.black
        mouth.lineWidth = 2
        mouth.position = CGPoint(x: 0, y: -5)
        mouth.zPosition = 1
        player.addChild(mouth)
        
        return player
    }
    
    /// 创建箱子
    private func createBox(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene else { return SKSpriteNode() }
        
        let box = SKSpriteNode(color: SKColor.brown, size: CGSize(width: scene.tileSize * 0.9, height: scene.tileSize * 0.9))
        box.position = position
        box.zPosition = 15
        box.name = "box"
        
        // 添加3D效果
        let highlight = SKSpriteNode(color: SKColor.white.withAlphaComponent(0.4), size: CGSize(width: scene.tileSize * 0.9, height: 6))
        highlight.position = CGPoint(x: 0, y: scene.tileSize * 0.9/2 - 3)
        highlight.zPosition = 1
        box.addChild(highlight)
        
        let shadow = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.4), size: CGSize(width: scene.tileSize * 0.9, height: 6))
        shadow.position = CGPoint(x: 0, y: -scene.tileSize * 0.9/2 + 3)
        shadow.zPosition = 1
        box.addChild(shadow)
        
        // 添加十字标记（根据README要求）
        let crossVertical = SKSpriteNode(color: SKColor.white.withAlphaComponent(0.8), size: CGSize(width: 3, height: scene.tileSize * 0.6))
        crossVertical.position = CGPoint(x: 0, y: 0)
        crossVertical.zPosition = 1
        box.addChild(crossVertical)
        
        let crossHorizontal = SKSpriteNode(color: SKColor.white.withAlphaComponent(0.8), size: CGSize(width: scene.tileSize * 0.6, height: 3))
        crossHorizontal.position = CGPoint(x: 0, y: 0)
        crossHorizontal.zPosition = 1
        box.addChild(crossHorizontal)
        
        return box
    }
    
    /// 创建目标点
    private func createTarget(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene else { return SKSpriteNode() }
        
        let target = SKSpriteNode(color: SKColor.clear, size: CGSize(width: scene.tileSize, height: scene.tileSize))
        target.position = position
        target.zPosition = 5
        target.name = "target"
        
        // 创建绿色圆环
        let ringPath = createRingPath(outerRadius: scene.tileSize * 0.4, innerRadius: scene.tileSize * 0.3)
        let ring = SKShapeNode(path: ringPath)
        ring.strokeColor = SKColor.systemGreen
        ring.fillColor = SKColor.systemGreen.withAlphaComponent(0.3)
        ring.lineWidth = 3
        ring.zPosition = 1
        target.addChild(ring)
        
        // 添加闪烁动画
        let fadeOut = SKAction.fadeAlpha(to: 0.3, duration: 1.0)
        let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 1.0)
        let pulse = SKAction.sequence([fadeOut, fadeIn])
        let repeatPulse = SKAction.repeatForever(pulse)
        target.run(repeatPulse)
        
        return target
    }
    
    /// 保存初始状态到管理器
    private func saveInitialStateToManager() {
        guard let scene = gameScene,
              let playerNode = scene.player else { return }
        
        let initialPlayerPosition = scene.gridPosition(for: playerNode.position)
        let initialBoxPositions = scene.boxes.map { scene.gridPosition(for: $0.position) }
        
        scene.gameStateManager.saveInitialState(
            playerPosition: initialPlayerPosition,
            boxPositions: initialBoxPositions
        )
        
        print("[LevelLoadingManager] Initial state saved to GameStateManager")
    }
    
    /// 显示游戏完成界面
    private func showGameCompletedScreen() {
        guard let scene = gameScene else { return }
        
        // 创建游戏完成界面
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.8), size: scene.size)
        overlay.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        overlay.zPosition = 200
        overlay.name = "gameCompletedOverlay"
        scene.addChild(overlay)
        
        let completedLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        completedLabel.text = "🎉 恭喜完成所有关卡! 🎉"
        completedLabel.fontSize = 28
        completedLabel.fontColor = SKColor.systemGreen
        completedLabel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        completedLabel.zPosition = 201
        completedLabel.name = "gameCompletedLabel"
        scene.addChild(completedLabel)
        
        // 添加彩色动画
        let colorCycle = createColorCycleAnimation()
        completedLabel.run(colorCycle)
    }
    
    // MARK: - Helper Methods
    
    /// 创建圆形纹理
    private func createCircleTexture(size: CGSize, color: SKColor) -> SKTexture {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            color.setFill()
            let rect = CGRect(origin: .zero, size: size)
            context.cgContext.fillEllipse(in: rect)
        }
        return SKTexture(image: image)
    }
    
    /// 创建笑脸路径
    private func createSmilePath() -> CGPath {
        let path = CGMutablePath()
        path.addArc(center: CGPoint.zero, radius: 8, startAngle: .pi * 0.2, endAngle: .pi * 0.8, clockwise: false)
        return path
    }
    
    /// 创建圆环路径
    private func createRingPath(outerRadius: CGFloat, innerRadius: CGFloat) -> CGPath {
        let path = CGMutablePath()
        path.addArc(center: CGPoint.zero, radius: outerRadius, startAngle: 0, endAngle: .pi * 2, clockwise: false)
        path.addArc(center: CGPoint.zero, radius: innerRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
        return path
    }

    /// 创建颜色循环动画
    private func createColorCycleAnimation() -> SKAction {
        let colors = [SKColor.systemRed, SKColor.systemOrange, SKColor.systemYellow, SKColor.systemGreen, SKColor.systemBlue, SKColor.systemPurple]
        var colorActions: [SKAction] = []

        for color in colors {
            let colorAction = SKAction.colorize(with: color, colorBlendFactor: 1.0, duration: 0.3)
            colorActions.append(colorAction)
        }

        let colorSequence = SKAction.sequence(colorActions)
        return SKAction.repeatForever(colorSequence)
    }
}
