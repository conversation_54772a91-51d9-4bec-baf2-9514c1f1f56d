//
//  TouchManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// 触摸处理管理器 - 负责所有触摸事件的处理和分发
class TouchManager {
    
    // MARK: - Properties
    private weak var gameScene: GameScene?
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
    }
    
    // MARK: - Public Methods
    
    /// 处理触摸开始事件
    /// - Parameters:
    ///   - touches: 触摸集合
    ///   - event: 触摸事件
    func handleTouchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first,
              let scene = gameScene else { return }
        
        let location = touch.location(in: scene)
        let touchedNode = scene.atPoint(location)
        
        // 1. 检查关卡选择菜单相关点击
        if handleLevelSelectionTouch(touchedNode: touchedNode, location: location) {
            return
        }
        
        // 2. 检查死锁警告相关点击
        if handleDeadlockAlertTouch(touchedNode: touchedNode) {
            return
        }
        
        // 3. 检查UI按钮点击
        if handleUIButtonTouch(location: location) {
            return
        }
        
        // 4. 处理游戏区域点击移动
        if !scene.isMoving {
            handleGameAreaTouch(location: location)
        }
    }
    
    // MARK: - Private Methods
    
    /// 处理关卡选择菜单触摸
    private func handleLevelSelectionTouch(touchedNode: SKNode, location: CGPoint) -> Bool {
        guard let scene = gameScene,
              let nodeName = touchedNode.name else { return false }
        
        // 关卡按钮点击
        if nodeName.hasPrefix("levelButton_") || nodeName.hasPrefix("levelNumber_") {
            let levelIndexString = nodeName.components(separatedBy: "_").last ?? "0"
            if let levelIndex = Int(levelIndexString) {
                scene.closeLevelSelectionMenu()
                scene.loadLevel(index: levelIndex)
            }
            return true
        }
        
        // 关闭按钮点击
        if nodeName == "levelSelectClose" || nodeName == "levelSelectCloseLabel" {
            scene.closeLevelSelectionMenu()
            return true
        }
        
        // 背景遮罩点击
        if nodeName == "levelSelectOverlay" {
            scene.closeLevelSelectionMenu()
            return true
        }
        
        return false
    }
    
    /// 处理死锁警告触摸
    private func handleDeadlockAlertTouch(touchedNode: SKNode) -> Bool {
        guard let scene = gameScene,
              let nodeName = touchedNode.name else { return false }
        
        // 重试按钮点击
        if nodeName == "deadlockRetry" || nodeName == "deadlockRetryLabel" {
            scene.closeDeadlockAlert()
            scene.resetLevel()
            return true
        }
        
        // 背景遮罩点击
        if nodeName == "deadlockOverlay" {
            scene.closeDeadlockAlert()
            return true
        }
        
        return false
    }
    
    /// 处理UI按钮触摸
    private func handleUIButtonTouch(location: CGPoint) -> Bool {
        guard let scene = gameScene else { return false }
        
        if let buttonType = scene.uiManager.handleButtonTouch(at: location) {
            scene.uiManager.animateButtonPress(buttonType: buttonType) {
                switch buttonType {
                case .retry:
                    scene.audioManager.playSound(.reset)
                    scene.resetLevel()
                case .undo:
                    scene.audioManager.playSound(.undo)
                    scene.undoLastMove()
                case .levelSelect:
                    scene.audioManager.playSound(.buttonPress)
                    scene.showLevelSelectionMenu()
                }
            }
            return true
        }
        
        return false
    }
    
    /// 处理游戏区域触摸
    private func handleGameAreaTouch(location: CGPoint) {
        guard let scene = gameScene,
              let playerNode = scene.player else { return }
        
        // 检查点击位置是否在游戏区域内
        guard scene.isLocationInGameArea(location) else {
            print("[TouchManager] 点击位置超出游戏区域，忽略移动")
            return
        }
        
        let playerGridPos = scene.gridPosition(for: playerNode.position)
        let targetGridPos = scene.gridPosition(for: location)
        
        // 计算移动方向
        let deltaX = targetGridPos.x - playerGridPos.x
        let deltaY = targetGridPos.y - playerGridPos.y
        
        // 确定主要移动方向 - 点击控制：选择距离更大的方向
        var direction: CGVector = .zero
        
        if abs(deltaX) > abs(deltaY) {
            // 水平移动
            direction = CGVector(dx: deltaX > 0 ? 1 : -1, dy: 0)
        } else if abs(deltaY) > 0 {
            // 垂直移动
            direction = CGVector(dx: 0, dy: deltaY > 0 ? 1 : -1)
        }
        
        if direction != .zero {
            // 显示移动路径
            scene.showMovementPath(from: playerGridPos, direction: direction)
            // 执行移动
            scene.movePlayer(direction: direction)
        }
    }
}
