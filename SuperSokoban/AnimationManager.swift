//
//  AnimationManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// 动画效果管理器 - 负责游戏中所有动画效果的创建和管理
class AnimationManager {
    
    // MARK: - Properties
    private weak var gameScene: GameScene?
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
    }
    
    // MARK: - Public Methods
    
    /// 播放胜利动画和音效
    func playWinAnimationAndSound() {
        guard let scene = gameScene else { return }
        
        // 创建胜利界面组件
        createVictoryOverlay()
        createVictoryPanel()
        createVictoryTitle()
        createVictoryStats()
        createNextLevelPreview()
        
        // 播放胜利音效
        scene.audioManager.playSound(.victory)
        
        // 播放入场动画
        playVictoryEntranceAnimation()
        
        // 播放背景动画效果
        playVictoryBackgroundEffects()
        
        // 自动清理
        scheduleVictoryCleanup()
    }
    
    /// 显示移动路径
    /// - Parameters:
    ///   - startPos: 起始位置
    ///   - direction: 移动方向
    func showMovementPath(from startPos: CGPoint, direction: CGVector) {
        guard let scene = gameScene else { return }
        
        // 清除之前的路径
        clearMovementPath()
        
        let targetPos = CGPoint(x: startPos.x + direction.dx, y: startPos.y + direction.dy)
        
        // 检查目标位置是否有效
        guard !scene.isWall(at: targetPos) else { return }
        
        // 创建路径指示器
        let pathIndicator = createPathIndicator(at: targetPos)
        scene.addChild(pathIndicator)
        
        // 添加闪烁动画
        let fadeOut = SKAction.fadeAlpha(to: 0.3, duration: 0.5)
        let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 0.5)
        let pulse = SKAction.sequence([fadeOut, fadeIn])
        let repeatPulse = SKAction.repeatForever(pulse)
        pathIndicator.run(repeatPulse)
        
        // 自动清理路径指示器
        let cleanup = SKAction.sequence([
            SKAction.wait(forDuration: 2.0),
            SKAction.removeFromParent()
        ])
        pathIndicator.run(cleanup)
    }
    
    /// 清除移动路径
    func clearMovementPath() {
        guard let scene = gameScene else { return }
        
        scene.children.filter { $0.name == "pathIndicator" }.forEach { $0.removeFromParent() }
    }
    
    /// 播放玩家移动动画
    /// - Parameters:
    ///   - player: 玩家节点
    ///   - targetPosition: 目标位置
    ///   - completion: 完成回调
    func playPlayerMoveAnimation(player: SKSpriteNode, to targetPosition: CGPoint, completion: @escaping () -> Void) {
        let moveAction = SKAction.move(to: targetPosition, duration: 0.15)
        moveAction.timingMode = .easeInEaseOut
        
        // 添加轻微的视觉反馈
        let wiggleRight = SKAction.rotate(byAngle: .pi / 32, duration: 0.04)
        let wiggleLeft = SKAction.rotate(byAngle: -.pi / 32, duration: 0.04)
        let straighten = SKAction.rotate(toAngle: 0, duration: 0.04)
        let wiggleSequence = SKAction.sequence([wiggleRight, wiggleLeft, straighten])
        
        let groupAction = SKAction.group([moveAction, wiggleSequence])
        
        player.run(groupAction) {
            completion()
        }
    }
    
    /// 播放箱子推动动画
    /// - Parameters:
    ///   - box: 箱子节点
    ///   - targetPosition: 目标位置
    ///   - completion: 完成回调
    func playBoxPushAnimation(box: SKSpriteNode, to targetPosition: CGPoint, completion: @escaping () -> Void) {
        let moveAction = SKAction.move(to: targetPosition, duration: 0.15)
        moveAction.timingMode = .easeInEaseOut
        
        // 添加箱子推动的视觉效果
        let scaleDown = SKAction.scale(to: 0.95, duration: 0.075)
        let scaleUp = SKAction.scale(to: 1.0, duration: 0.075)
        let scaleSequence = SKAction.sequence([scaleDown, scaleUp])
        
        let groupAction = SKAction.group([moveAction, scaleSequence])
        
        box.run(groupAction) {
            completion()
        }
    }
    
    /// 播放死锁警告动画
    func showDeadlockAlert() {
        guard let scene = gameScene else { return }
        
        // 创建警告界面组件
        createDeadlockOverlay()
        createDeadlockPanel()
        createDeadlockTitle()
        createDeadlockMessage()
        createDeadlockRetryButton()
        
        // 播放警告音效
        scene.audioManager.playSound(.error)
        
        // 播放入场动画
        playDeadlockEntranceAnimation()
    }
    
    /// 关闭死锁警告
    func closeDeadlockAlert() {
        guard let scene = gameScene else { return }
        
        // 移除所有死锁警告相关的节点
        scene.children.filter { node in
            node.name?.contains("deadlock") == true
        }.forEach { $0.removeFromParent() }
    }
    
    // MARK: - Private Methods - Victory Animation
    
    /// 创建胜利背景遮罩
    private func createVictoryOverlay() {
        guard let scene = gameScene else { return }
        
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.6), size: scene.size)
        overlay.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        overlay.zPosition = 150
        overlay.name = "victoryOverlay"
        scene.addChild(overlay)
    }
    
    /// 创建胜利面板
    private func createVictoryPanel() {
        guard let scene = gameScene else { return }
        
        let victoryPanel = SKShapeNode(rectOf: CGSize(width: 300, height: 200), cornerRadius: 20)
        victoryPanel.fillColor = SKColor.white.withAlphaComponent(0.95)
        victoryPanel.strokeColor = SKColor.systemGreen
        victoryPanel.lineWidth = 4
        victoryPanel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        victoryPanel.zPosition = 151
        victoryPanel.name = "victoryPanel"
        scene.addChild(victoryPanel)
    }
    
    /// 创建胜利标题
    private func createVictoryTitle() {
        guard let scene = gameScene else { return }
        
        let victoryLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        victoryLabel.text = "🎉 恭喜过关! 🎉"
        victoryLabel.fontSize = 24
        victoryLabel.fontColor = SKColor.systemGreen
        victoryLabel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2 + 40)
        victoryLabel.zPosition = 152
        victoryLabel.name = "victoryLabel"
        scene.addChild(victoryLabel)
    }
    
    /// 创建胜利统计信息
    private func createVictoryStats() {
        guard let scene = gameScene else { return }
        
        let moveCount = scene.gameStateManager.getCurrentMoveCount()
        let statsLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        statsLabel.text = "移动步数: \(moveCount)"
        statsLabel.fontSize = 18
        statsLabel.fontColor = SKColor.darkGray
        statsLabel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        statsLabel.zPosition = 152
        statsLabel.name = "victoryStats"
        scene.addChild(statsLabel)
    }
    
    /// 创建下一关预览
    private func createNextLevelPreview() {
        guard let scene = gameScene else { return }
        
        let nextLevelLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        let totalLevels = scene.levelGenerator.numberOfLevels + 5 // 包含教学关卡
        if scene.currentLevelIndex + 1 < totalLevels {
            nextLevelLabel.text = "准备进入关卡 \(scene.currentLevelIndex + 2)/\(totalLevels)..."
        } else {
            nextLevelLabel.text = "恭喜完成所有\(totalLevels)关!"
        }
        nextLevelLabel.fontSize = 16
        nextLevelLabel.fontColor = SKColor.blue
        nextLevelLabel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2 - 40)
        nextLevelLabel.zPosition = 152
        nextLevelLabel.name = "nextLevelLabel"
        scene.addChild(nextLevelLabel)
    }
    
    /// 播放胜利入场动画
    private func playVictoryEntranceAnimation() {
        guard let scene = gameScene else { return }
        
        if let victoryPanel = scene.childNode(withName: "victoryPanel") {
            victoryPanel.setScale(0)
            let scaleUp = SKAction.scale(to: 1.0, duration: 0.4)
            scaleUp.timingMode = .easeOut
            victoryPanel.run(scaleUp)
        }
        
        if let victoryLabel = scene.childNode(withName: "victoryLabel") {
            let colorCycle = createColorCycleAnimation()
            victoryLabel.run(colorCycle)
        }
    }
    
    /// 播放胜利背景效果
    private func playVictoryBackgroundEffects() {
        guard let scene = gameScene else { return }
        
        // 创建庆祝粒子效果（简化版）
        for _ in 0..<10 {
            let particle = createCelebrationParticle()
            scene.addChild(particle)
            
            let randomX = CGFloat.random(in: 0...scene.size.width)
            let randomY = scene.size.height + 50
            particle.position = CGPoint(x: randomX, y: randomY)
            
            let fallAction = SKAction.moveTo(y: -50, duration: 3.0)
            let removeAction = SKAction.removeFromParent()
            let sequence = SKAction.sequence([fallAction, removeAction])
            particle.run(sequence)
        }
    }
    
    /// 安排胜利界面清理
    private func scheduleVictoryCleanup() {
        guard let scene = gameScene else { return }
        
        let cleanup = SKAction.sequence([
            SKAction.wait(forDuration: 2.0),
            SKAction.run {
                scene.children.filter { node in
                    node.name?.contains("victory") == true ||
                    node.name?.contains("celebration") == true
                }.forEach { $0.removeFromParent() }
            }
        ])
        scene.run(cleanup)
    }
    
    // MARK: - Private Methods - Deadlock Animation
    
    /// 创建死锁警告背景遮罩
    private func createDeadlockOverlay() {
        guard let scene = gameScene else { return }
        
        let overlay = SKSpriteNode(color: SKColor.red.withAlphaComponent(0.3), size: scene.size)
        overlay.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        overlay.zPosition = 180
        overlay.name = "deadlockOverlay"
        scene.addChild(overlay)
    }
    
    /// 创建死锁警告面板
    private func createDeadlockPanel() {
        guard let scene = gameScene else { return }
        
        let alertPanel = SKShapeNode(rectOf: CGSize(width: 280, height: 160), cornerRadius: 15)
        alertPanel.fillColor = SKColor.white.withAlphaComponent(0.95)
        alertPanel.strokeColor = SKColor.systemRed
        alertPanel.lineWidth = 3
        alertPanel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        alertPanel.zPosition = 181
        alertPanel.name = "deadlockPanel"
        scene.addChild(alertPanel)
    }
    
    /// 创建死锁警告标题
    private func createDeadlockTitle() {
        guard let scene = gameScene else { return }
        
        let alertTitle = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        alertTitle.text = "⚠️ 死锁警告"
        alertTitle.fontSize = 22
        alertTitle.fontColor = SKColor.systemRed
        alertTitle.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2 + 30)
        alertTitle.zPosition = 182
        alertTitle.name = "deadlockTitle"
        scene.addChild(alertTitle)
    }
    
    /// 创建死锁警告消息
    private func createDeadlockMessage() {
        guard let scene = gameScene else { return }
        
        let alertMessage = SKLabelNode(fontNamed: "HelveticaNeue")
        alertMessage.text = "当前状态无解，需要重新开始"
        alertMessage.fontSize = 16
        alertMessage.fontColor = SKColor.darkGray
        alertMessage.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        alertMessage.zPosition = 182
        alertMessage.name = "deadlockMessage"
        scene.addChild(alertMessage)
    }
    
    /// 创建死锁重试按钮
    private func createDeadlockRetryButton() {
        guard let scene = gameScene else { return }
        
        let retryButton = SKShapeNode(rectOf: CGSize(width: 100, height: 35), cornerRadius: 17)
        retryButton.fillColor = SKColor.systemRed
        retryButton.strokeColor = SKColor.white
        retryButton.lineWidth = 2
        retryButton.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2 - 40)
        retryButton.zPosition = 182
        retryButton.name = "deadlockRetry"
        scene.addChild(retryButton)
        
        let retryLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        retryLabel.text = "重试"
        retryLabel.fontSize = 16
        retryLabel.fontColor = SKColor.white
        retryLabel.horizontalAlignmentMode = .center
        retryLabel.verticalAlignmentMode = .center
        retryLabel.position = retryButton.position
        retryLabel.zPosition = 183
        retryLabel.name = "deadlockRetryLabel"
        scene.addChild(retryLabel)
    }
    
    /// 播放死锁警告入场动画
    private func playDeadlockEntranceAnimation() {
        guard let scene = gameScene else { return }
        
        if let alertPanel = scene.childNode(withName: "deadlockPanel") {
            alertPanel.setScale(0)
            let scaleUp = SKAction.scale(to: 1.0, duration: 0.3)
            scaleUp.timingMode = .easeOut
            alertPanel.run(scaleUp)
        }
        
        // 添加震动效果
        let shake = createShakeAnimation()
        scene.run(shake)
    }
    
    // MARK: - Private Methods - Helper Functions
    
    /// 创建路径指示器
    private func createPathIndicator(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene else { return SKSpriteNode() }
        
        let pathNode = SKSpriteNode(color: SKColor.yellow.withAlphaComponent(0.8), size: CGSize(width: scene.tileSize * 0.6, height: scene.tileSize * 0.6))
        pathNode.position = scene.scenePosition(for: position)
        pathNode.zPosition = 5
        pathNode.name = "pathIndicator"
        
        return pathNode
    }
    

    
    /// 创建庆祝粒子
    private func createCelebrationParticle() -> SKSpriteNode {
        let colors = [SKColor.systemRed, SKColor.systemOrange, SKColor.systemYellow, SKColor.systemGreen, SKColor.systemBlue]
        let randomColor = colors.randomElement() ?? SKColor.white
        
        let particle = SKSpriteNode(color: randomColor, size: CGSize(width: 8, height: 8))
        particle.name = "celebrationParticle"
        particle.zPosition = 155
        
        // 添加旋转动画
        let rotate = SKAction.rotate(byAngle: .pi * 2, duration: 1.0)
        let repeatRotate = SKAction.repeatForever(rotate)
        particle.run(repeatRotate)
        
        return particle
    }
    
    /// 创建震动动画
    private func createShakeAnimation() -> SKAction {
        let shakeDistance: CGFloat = 5
        let shakeDuration: TimeInterval = 0.1

        let shakeRight = SKAction.moveBy(x: shakeDistance, y: 0, duration: shakeDuration)
        let shakeLeft = SKAction.moveBy(x: -shakeDistance * 2, y: 0, duration: shakeDuration)
        let shakeReturn = SKAction.moveBy(x: shakeDistance, y: 0, duration: shakeDuration)

        let shakeSequence = SKAction.sequence([shakeRight, shakeLeft, shakeReturn])
        return SKAction.repeat(shakeSequence, count: 3)
    }

    /// 创建颜色循环动画（公共方法）
    func createColorCycleAnimation() -> SKAction {
        let colors = [SKColor.systemRed, SKColor.systemOrange, SKColor.systemYellow, SKColor.systemGreen, SKColor.systemBlue, SKColor.systemPurple]
        var colorActions: [SKAction] = []

        for color in colors {
            let colorAction = SKAction.colorize(with: color, colorBlendFactor: 1.0, duration: 0.3)
            colorActions.append(colorAction)
        }

        let colorSequence = SKAction.sequence(colorActions)
        return SKAction.repeatForever(colorSequence)
    }
}
