//
//  UIManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// UI管理器 - 负责游戏界面元素的创建和管理
class UIManager {

    // MARK: - Properties
    private weak var gameScene: GameScene?
    private var screenAdaptationManager: ScreenAdaptationManager?
    private var levelLabel: SKLabelNode?
    private var retryButton: SKLabelNode?
    private var undoButton: SKLabelNode?
    private var levelSelectButton: SKLabelNode?
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
        self.screenAdaptationManager = ScreenAdaptationManager(gameScene: gameScene)
    }
    
    // MARK: - Public Methods
    
    /// 设置所有UI元素
    func setupAllUIElements(safeAreaInsets: UIEdgeInsets, currentLevel: Int) {
        guard let scene = gameScene else { return }

        setupLevelLabel(scene: scene, safeAreaInsets: safeAreaInsets, currentLevel: currentLevel)
        setupRetryButton(scene: scene, safeAreaInsets: safeAreaInsets)
        setupUndoButton(scene: scene, safeAreaInsets: safeAreaInsets)
        setupLevelSelectButton(scene: scene, safeAreaInsets: safeAreaInsets)
    }
    
    /// 更新关卡标签
    func updateLevelLabel(currentLevel: Int) {
        levelLabel?.text = "关卡 \(currentLevel + 1)"
    }
    
    /// 检查按钮点击
    func handleButtonTouch(at location: CGPoint) -> UIButtonType? {
        if let button = retryButton, button.contains(location) {
            return .retry
        }

        if let button = undoButton, button.contains(location) {
            return .undo
        }

        if let button = levelSelectButton, button.contains(location) {
            return .levelSelect
        }

        return nil
    }
    
    /// 播放按钮按压动画
    func animateButtonPress(buttonType: UIButtonType, completion: @escaping () -> Void) {
        let button: SKNode?

        switch buttonType {
        case .retry:
            button = retryButton
        case .undo:
            button = undoButton
        case .levelSelect:
            button = levelSelectButton
        }

        guard let targetButton = button else {
            completion()
            return
        }
        
        // 按压动画
        let scaleDown = SKAction.scale(to: 0.9, duration: 0.1)
        let scaleUp = SKAction.scale(to: 1.0, duration: 0.1)
        
        // 颜色闪烁效果
        let originalColor = (targetButton as? SKLabelNode)?.fontColor ?? SKColor.white
        let flashColor = SKColor.yellow
        let colorFlash = SKAction.colorize(with: flashColor, colorBlendFactor: 0.5, duration: 0.1)
        let colorRestore = SKAction.colorize(with: originalColor, colorBlendFactor: 1.0, duration: 0.1)
        
        // 组合动画
        let pressAnimation = SKAction.sequence([
            SKAction.group([scaleDown, colorFlash]),
            SKAction.group([scaleUp, colorRestore])
        ])
        
        targetButton.run(pressAnimation) {
            completion()
        }
    }
    
    // MARK: - Private Methods
    
    /// 设置关卡标签
    private func setupLevelLabel(scene: GameScene, safeAreaInsets: UIEdgeInsets, currentLevel: Int) {
        guard let adaptationManager = screenAdaptationManager else { return }

        let screenInfo = adaptationManager.getCurrentScreenInfo()
        let uiScale = screenInfo.scaleFactor

        // 创建现代化背景，大小根据屏幕适配
        let backgroundSize = adaptationManager.getAdaptedUISize(baseSize: CGSize(width: 120, height: 40), uiScale: uiScale)
        let background = SKShapeNode(rectOf: backgroundSize, cornerRadius: 12 * uiScale)
        background.fillColor = SKColor(red: 0.2, green: 0.4, blue: 0.8, alpha: 0.95)
        background.strokeColor = SKColor.white.withAlphaComponent(0.8)
        background.lineWidth = 2.5 * uiScale
        background.position = CGPoint(x: 80 * uiScale, y: scene.size.height - safeAreaInsets.top - 35 * uiScale)
        background.zPosition = 99
        scene.addChild(background)

        // 添加微妙的脉冲动画
        let pulseUp = SKAction.scale(to: 1.03, duration: 2.0)
        let pulseDown = SKAction.scale(to: 1.0, duration: 2.0)
        let pulseSequence = SKAction.sequence([pulseUp, pulseDown])
        let pulseForever = SKAction.repeatForever(pulseSequence)
        background.run(pulseForever)

        levelLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        levelLabel?.text = "关卡 \(currentLevel + 1)"
        levelLabel?.fontSize = adaptationManager.getAdaptedFontSize(baseFontSize: 20, fontScale: uiScale)
        levelLabel?.fontColor = SKColor.white
        levelLabel?.horizontalAlignmentMode = .center
        levelLabel?.verticalAlignmentMode = .center
        levelLabel?.position = background.position
        levelLabel?.zPosition = 100

        if let label = levelLabel {
            scene.addChild(label)
        }
    }
    
    /// 设置重试按钮
    private func setupRetryButton(scene: GameScene, safeAreaInsets: UIEdgeInsets) {
        let buttonBackground = SKShapeNode(rectOf: CGSize(width: 80, height: 40), cornerRadius: 12)
        buttonBackground.fillColor = SKColor(red: 0.95, green: 0.4, blue: 0.4, alpha: 0.95)
        buttonBackground.strokeColor = SKColor.white.withAlphaComponent(0.8)
        buttonBackground.lineWidth = 2.5
        buttonBackground.position = CGPoint(x: scene.size.width - 55, y: scene.size.height - safeAreaInsets.top - 35)
        buttonBackground.zPosition = 99
        buttonBackground.name = "retryButtonBackground"
        scene.addChild(buttonBackground)

        // 添加微妙的脉冲动画
        let pulseUp = SKAction.scale(to: 1.05, duration: 1.5)
        let pulseDown = SKAction.scale(to: 1.0, duration: 1.5)
        let pulseSequence = SKAction.sequence([pulseUp, pulseDown])
        let pulseForever = SKAction.repeatForever(pulseSequence)
        buttonBackground.run(pulseForever)

        retryButton = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        retryButton?.text = "重试"
        retryButton?.fontSize = 17
        retryButton?.fontColor = SKColor.white
        retryButton?.name = "retryButton"
        retryButton?.horizontalAlignmentMode = .center
        retryButton?.verticalAlignmentMode = .center
        retryButton?.position = buttonBackground.position
        retryButton?.zPosition = 100

        if let button = retryButton {
            scene.addChild(button)
        }
    }

    /// 设置回退按钮 (绿色)
    private func setupUndoButton(scene: GameScene, safeAreaInsets: UIEdgeInsets) {
        // 按钮背景
        let buttonBackground = SKShapeNode(rectOf: CGSize(width: 80, height: 40), cornerRadius: 20)
        buttonBackground.fillColor = SKColor.systemGreen
        buttonBackground.strokeColor = SKColor.white
        buttonBackground.lineWidth = 2
        buttonBackground.position = CGPoint(x: scene.size.width - 100, y: scene.size.height - safeAreaInsets.top - 100)
        buttonBackground.zPosition = 99
        buttonBackground.name = "undoButtonBackground"
        scene.addChild(buttonBackground)

        // 添加微妙的脉冲动画
        let pulseUp = SKAction.scale(to: 1.05, duration: 1.5)
        let pulseDown = SKAction.scale(to: 1.0, duration: 1.5)
        let pulseSequence = SKAction.sequence([pulseUp, pulseDown])
        let pulseForever = SKAction.repeatForever(pulseSequence)
        buttonBackground.run(pulseForever)

        undoButton = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        undoButton?.text = "回退"
        undoButton?.fontSize = 17
        undoButton?.fontColor = SKColor.white
        undoButton?.name = "undoButton"
        undoButton?.horizontalAlignmentMode = .center
        undoButton?.verticalAlignmentMode = .center
        undoButton?.position = buttonBackground.position
        undoButton?.zPosition = 100

        if let button = undoButton {
            scene.addChild(button)
        }
    }

    /// 设置选关按钮 (紫色)
    private func setupLevelSelectButton(scene: GameScene, safeAreaInsets: UIEdgeInsets) {
        // 按钮背景
        let buttonBackground = SKShapeNode(rectOf: CGSize(width: 80, height: 40), cornerRadius: 20)
        buttonBackground.fillColor = SKColor.systemPurple
        buttonBackground.strokeColor = SKColor.white
        buttonBackground.lineWidth = 2
        buttonBackground.position = CGPoint(x: scene.size.width - 200, y: scene.size.height - safeAreaInsets.top - 100)
        buttonBackground.zPosition = 99
        buttonBackground.name = "levelSelectButtonBackground"
        scene.addChild(buttonBackground)

        // 添加微妙的脉冲动画
        let pulseUp = SKAction.scale(to: 1.05, duration: 1.5)
        let pulseDown = SKAction.scale(to: 1.0, duration: 1.5)
        let pulseSequence = SKAction.sequence([pulseUp, pulseDown])
        let pulseForever = SKAction.repeatForever(pulseSequence)
        buttonBackground.run(pulseForever)

        levelSelectButton = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        levelSelectButton?.text = "选关"
        levelSelectButton?.fontSize = 17
        levelSelectButton?.fontColor = SKColor.white
        levelSelectButton?.name = "levelSelectButton"
        levelSelectButton?.horizontalAlignmentMode = .center
        levelSelectButton?.verticalAlignmentMode = .center
        levelSelectButton?.position = buttonBackground.position
        levelSelectButton?.zPosition = 100

        if let button = levelSelectButton {
            scene.addChild(button)
        }
    }

}

/// UI按钮类型枚举
enum UIButtonType {
    case retry      // 红色重置按钮
    case undo       // 绿色回退按钮
    case levelSelect // 紫色选关按钮
}
