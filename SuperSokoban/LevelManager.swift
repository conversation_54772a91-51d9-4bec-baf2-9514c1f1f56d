//
//  LevelManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation

/// 关卡管理器 - 负责关卡数据管理和提供
class LevelManager {
    private var levels: [[[Character]]] = []

    init() {
        setupDefaultLevels()
    }

    /// 设置默认的教学关卡
    private func setupDefaultLevels() {
        // 关卡 1: 简单入门 - 小网格
        levels.append([
            ["W", "W", "W", "W", "W"],
            ["W", "P", "G", "T", "W"],
            ["W", "G", "B", "G", "W"],
            ["W", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W"]
        ])

        // 关卡 2: 中等难度 - 大网格，需要策略
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "G", "G", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "G", "T", "W", "G", "W"],
            ["W", "G", "G", "G", "B", "B", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "G", "P", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "G", "T", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 3: 复杂迷宫 - 需要精确规划
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "B", "G", "W", "G", "W", "W", "W", "T", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "G", "W", "G", "W", "G", "B", "W", "W", "W"],
            ["W", "T", "G", "G", "W", "G", "G", "G", "G", "W", "T", "W"],
            ["W", "G", "G", "P", "W", "G", "W", "G", "G", "G", "G", "W"],
            ["W", "G", "B", "G", "G", "G", "W", "G", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "T", "W", "W", "W", "W", "W", "W", "W", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 4: 高难度推理 - 多箱子协调
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "W", "G", "B", "B", "G", "W", "T", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "W", "W", "G", "G", "W", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "P", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "W", "G", "G", "W", "W", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "W", "G", "B", "B", "G", "W", "T", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 5: 终极挑战 - 大型复杂关卡
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "W", "G", "B", "B", "G", "W", "G", "T", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "W", "W", "W", "G", "G", "W", "W", "W", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "B", "G", "G", "W", "G", "G", "W", "G", "G", "B", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "P", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "B", "G", "G", "W", "G", "G", "W", "G", "G", "B", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "W", "W", "W", "G", "G", "W", "W", "W", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "W", "G", "B", "B", "G", "W", "G", "T", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 后续可以从文件或网络加载更多关卡
    }

    /// 获取指定索引的关卡数据
    /// - Parameter index: 关卡索引
    /// - Returns: 关卡数据 (二维字符数组)，如果索引无效则返回nil
    func getLevel(at index: Int) -> [[Character]]? {
        guard index >= 0 && index < levels.count else {
            return nil
        }
        return levels[index]
    }

    /// 获取总关卡数
    var numberOfLevels: Int {
        return levels.count
    }
    
    // TODO: 添加从文件加载关卡的功能
    // func loadLevelsFromFile(fileName: String) { ... }
}
