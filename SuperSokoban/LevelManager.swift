//
//  LevelManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation

/// 关卡管理器 - 负责关卡数据管理和提供
class LevelManager {
    private var levels: [[[Character]]] = []

    init() {
        setupDefaultLevels()
    }

    /// 设置默认的教学关卡 - 根据README要求：所有关卡都是16x17网格
    private func setupDefaultLevels() {
        // 关卡 1: 16x17基础教学 - 简单入门
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "P", "G", "B", "G", "T", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 2: 16x17策略思考 - 中等难度
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "T", "G", "G", "G", "G", "T", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "B", "B", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "P", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 3: 16x17迷宫 - 精确规划
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "B", "G", "G", "G", "G", "G", "G", "B", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "W", "W", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "T", "T", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "G", "G", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "P", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "G", "G", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "T", "T", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "W", "W", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "B", "G", "G", "G", "G", "G", "G", "B", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 4: 16x17高难度 - 多箱子协调
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "W", "G", "G", "G", "G", "G", "G", "W", "T", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "B", "G", "G", "B", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "W", "G", "G", "G", "G", "W", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "P", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "W", "G", "G", "G", "G", "W", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "B", "G", "G", "B", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "W", "G", "G", "G", "G", "G", "G", "W", "T", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "G", "G", "G", "G", "G", "G", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 关卡 5: 16x17终极挑战 - 大型复杂关卡
        levels.append([
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "W", "G", "G", "G", "G", "W", "G", "T", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "B", "B", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "W", "W", "W", "G", "G", "W", "W", "W", "G", "W", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "G", "G", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "P", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "W", "G", "G", "W", "G", "G", "G", "G", "G", "W"],
            ["W", "G", "G", "G", "W", "G", "G", "G", "G", "G", "G", "W", "G", "G", "G", "W"],
            ["W", "G", "W", "G", "W", "W", "W", "G", "G", "W", "W", "W", "G", "W", "G", "W"],
            ["W", "G", "W", "G", "G", "G", "G", "B", "B", "G", "G", "G", "G", "W", "G", "W"],
            ["W", "G", "W", "T", "G", "W", "G", "G", "G", "G", "W", "G", "T", "W", "G", "W"],
            ["W", "G", "W", "W", "W", "W", "G", "G", "G", "G", "W", "W", "W", "W", "G", "W"],
            ["W", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W", "W"]
        ])

        // 后续可以从文件或网络加载更多关卡
    }

    /// 获取指定索引的关卡数据
    /// - Parameter index: 关卡索引
    /// - Returns: 关卡数据 (二维字符数组)，如果索引无效则返回nil
    func getLevel(at index: Int) -> [[Character]]? {
        guard index >= 0 && index < levels.count else {
            return nil
        }
        return levels[index]
    }

    /// 获取总关卡数
    var numberOfLevels: Int {
        return levels.count
    }
    
    // TODO: 添加从文件加载关卡的功能
    // func loadLevelsFromFile(fileName: String) { ... }
}
