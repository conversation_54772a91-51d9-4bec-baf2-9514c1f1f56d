//
//  AudioManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit
import AudioToolbox

/// 音效类型枚举
enum SoundType {
    case push           // 推箱子
    case victory        // 胜利
    case failure        // 失败
    case undo          // 回退
    case reset         // 重置
    case buttonPress   // 按钮按压
    case error         // 错误
}

/// 音效管理器 - 负责游戏音效的播放和管理
class AudioManager {
    
    // MARK: - Properties
    private weak var gameScene: GameScene?
    private var isAudioEnabled: Bool = true
    
    // 音效文件映射
    private let soundFileMap: [SoundType: String] = [
        .push: "push.wav",
        .victory: "win.wav",
        .failure: "failure.wav",
        .undo: "undo.wav",
        .reset: "reset.wav",
        .buttonPress: "button_press.wav",
        .error: "error.wav"
    ]
    
    // 系统音效映射（当自定义音效不存在时使用）
    private let systemSoundMap: [SoundType: SystemSoundID] = [
        .push: 1104,        // Tock sound for box pushing
        .victory: 1016,     // Success sound for level completion
        .failure: 1053,     // Error sound for failure
        .undo: 1105,        // Soft click for undo
        .reset: 1103,       // Reset sound
        .buttonPress: 1104, // Button press sound
        .error: 1053        // Error sound
    ]
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
    }
    
    // MARK: - Public Methods
    
    /// 播放指定类型的音效
    func playSound(_ soundType: SoundType) {
        guard isAudioEnabled else { return }
        
        let fileName = soundFileMap[soundType] ?? "default.wav"
        
        // 检查自定义音效文件是否存在
        if Bundle.main.path(forResource: fileName.replacingOccurrences(of: ".wav", with: ""), ofType: "wav") != nil {
            // 播放自定义音效
            playCustomSound(fileName)
            print("[AudioManager] 播放自定义音效: \(fileName)")
        } else {
            // 播放系统音效
            playSystemSound(soundType)
            print("[AudioManager] 播放系统音效: \(soundType)")
        }
    }
    
    /// 启用/禁用音效
    func setAudioEnabled(_ enabled: Bool) {
        isAudioEnabled = enabled
        print("[AudioManager] 音效已\(enabled ? "启用" : "禁用")")
    }
    
    /// 检查音效是否启用
    func isAudioEnabledStatus() -> Bool {
        return isAudioEnabled
    }
    
    /// 播放胜利音效序列（多个音效组合）
    func playVictorySequence() {
        guard isAudioEnabled else { return }
        
        // 播放主要胜利音效
        playSound(.victory)
        
        // 延迟播放额外的庆祝音效
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.playSound(.buttonPress) // 额外的庆祝音效
        }
    }
    
    /// 播放失败音效序列
    func playFailureSequence() {
        guard isAudioEnabled else { return }
        
        playSound(.failure)
        
        // 播放额外的错误提示音
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.playSound(.error)
        }
    }
    
    // MARK: - Private Methods
    
    /// 播放自定义音效文件
    private func playCustomSound(_ fileName: String) {
        guard let scene = gameScene else { return }
        
        let soundAction = SKAction.playSoundFileNamed(fileName, waitForCompletion: false)
        scene.run(soundAction)
    }
    
    /// 播放系统音效
    private func playSystemSound(_ soundType: SoundType) {
        let systemSoundID = systemSoundMap[soundType] ?? 1057 // 默认系统音效
        AudioServicesPlaySystemSound(systemSoundID)
    }
    
    /// 预加载音效文件（可选优化）
    func preloadSounds() {
        print("[AudioManager] 开始预加载音效文件...")
        
        for (soundType, fileName) in soundFileMap {
            if Bundle.main.path(forResource: fileName.replacingOccurrences(of: ".wav", with: ""), ofType: "wav") != nil {
                print("[AudioManager] 找到音效文件: \(fileName)")
            } else {
                print("[AudioManager] 音效文件不存在，将使用系统音效: \(soundType)")
            }
        }
        
        print("[AudioManager] 音效预加载完成")
    }
    
    /// 获取音效状态报告
    func getAudioStatusReport() -> String {
        var report = "[AudioManager] 音效状态报告:\n"
        report += "- 音效启用状态: \(isAudioEnabled)\n"
        report += "- 可用的自定义音效:\n"
        
        for (soundType, fileName) in soundFileMap {
            let exists = Bundle.main.path(forResource: fileName.replacingOccurrences(of: ".wav", with: ""), ofType: "wav") != nil
            report += "  - \(soundType): \(fileName) (\(exists ? "存在" : "不存在，使用系统音效"))\n"
        }
        
        return report
    }
}
