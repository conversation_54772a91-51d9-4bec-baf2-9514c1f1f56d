# SuperSokoban - 超级推箱子游戏

一个功能完整、视觉精美的iOS推箱子游戏，采用Swift和SpriteKit开发。

## 🎮 游戏特色

### 核心玩法
- **经典推箱子规则**：将所有箱子推到目标位置即可过关
- **5个精心设计的关卡**：从简单入门到终极挑战
- **智能难度递增**：关卡大网格高难度
- **点击控制**：点击屏幕任意位置，小人会向该方向移动，小人不能跑到目标位置上
- **移动路径显示**：黄色指示器显示移动方向，带闪烁动画

### 视觉设计
- **可爱角色**：橙色圆形小人，带有眼睛和嘴巴表情
- **醒目目标**：绿色圆环目标点，带有闪烁动画效果
- **3D效果元素**：
  - 箱子：棕色3D效果，带十字标记
  - 墙壁：深棕色3D效果，有高光和阴影
  - 地面：浅色纹理，细微边框装饰
- **渐变背景**：深蓝色渐变，营造舒适游戏氛围
- **安全区域适配**：完美适配iPhone刘海屏和各种尺寸

### 用户界面
- **关卡显示**：左上角蓝色背景关卡标签
- **功能按钮**：
  - 🔴 重置按钮：红色，重置当前关卡到初始状态
  - 🟢 回退按钮：绿色，回退一步操作，回退的时候是从当前位置知道让一个位置
  - 🟣 选关按钮：紫色，打开关卡选择菜单
- **关卡选择菜单**：
  - 半透明背景遮罩
  - 网格布局的关卡按钮
  - 当前关卡高亮显示
  - 点击任意关卡直接跳转

### 音效系统
- **系统音效回退**：使用系统音效
- **多种音效**：
  - 推箱子音效：移动箱子时播放
  - 胜利音效：完成关卡时播放
  - 失败音效：失败时播放
  - 重置音效：重置关卡时播放
  - 回退音效：回退操作时播放

## 🏗️ 技术架构

### 项目结构
```
SuperSokoban/
├── AppDelegate.swift          # 应用程序委托
├── SceneDelegate.swift        # 场景委托，处理安全区域适配
├── ViewController.swift       # 主视图控制器
├── GameScene.swift           # 游戏场景主逻辑
├── Assets.xcassets/          # 图片资源
├── Base.lproj/              # 界面文件
│   ├── Main.storyboard      # 主界面
│   └── LaunchScreen.storyboard # 启动界面
├── Info.plist               # 应用配置
├── push.wav                 # 推箱子音效
├── win.wav                  # 胜利音效
└── SparkParticle.sks        # 粒子效果文件
```

### 核心类设计

#### GameScene (主游戏场景)
- **职责**：游戏逻辑控制、用户交互处理、管理器协调
- **关键属性**：
  - `player`: 玩家节点
  - `boxes`: 箱子节点数组
  - `targets`: 目标点节点数组
  - `gameStateManager`: 游戏状态管理器
  - `deadlockDetector`: 死锁检测器
  - `uiManager`: UI管理器
  - `audioManager`: 音效管理器
  - `tileSize`: 动态瓦片大小
- **核心方法**：
  - `loadLevel()`: 加载关卡
  - `movePlayer()`: 移动玩家
  - `resetLevel()`: 重置关卡
  - `undoLastMove()`: 回退操作
  - `checkWinCondition()`: 检查胜利条件

#### GameStateManager (游戏状态管理器)
- **职责**：状态保存、回退和重置管理
- **核心功能**：
  - 保存初始状态和移动历史
  - 支持回退到上一个状态
  - 限制历史记录数量（最多50步）
  - 提供状态统计信息

#### DeadlockDetector (死锁检测器)
- **职责**：检测游戏中的无解状态
  - 每走一步都要去检查是否处于无解状态
- **检测类型**：
  - 角落死锁：箱子被推到角落无法移动
  - 箱子组合死锁：多个箱子形成无法移动的组合
  - 墙边死锁：箱子靠墙且无可达目标

#### UIManager (UI管理器)
- **职责**：界面元素的创建和管理
- **管理内容**：
  - 关卡标签显示
  - 重置和回退按钮
  - 按钮动画效果
  - 触摸事件处理

#### AudioManager (音效管理器)
- **职责**：音效播放和管理
- **功能特性**：
  - 自定义音效和系统音效智能切换
  - 音效序列播放
  - 音效开关控制
  - 音效预加载

#### LevelManager (关卡管理器)
- **职责**：关卡数据管理和提供
- **关卡设计原则**：
  - 关卡1：16x17小网格，基础教学
  - 关卡2：16x17大网格，策略思考
  - 关卡3：16x17迷宫，精确规划
  - 关卡4：16x17高难度，多箱子协调
  - 关卡5：16x17终极挑战，大型复杂关卡

### 游戏元素编码
- `P`: Player (玩家起始位置)
- `B`: Box (箱子)
- `T`: Target (目标点)
- `W`: Wall (墙壁)
- `G`: Ground (地面)

## 🎯 游戏机制

### 移动系统
1. **点击检测**：检测用户点击位置
2. **方向计算**：计算主要移动方向（水平/垂直）
3. **路径显示**：显示黄色移动指示器
4. **碰撞检测**：检查目标位置是否可移动
5. **箱子推动**：如果推动箱子，检查箱子后方是否有空间
6. **状态保存**：保存移动后的状态到历史记录

### 关卡设计哲学
1. **渐进式难度**：从简单的直推到复杂的多步规划
2. **网格大小递增**：关卡越高，网格越大，策略性越强
3. **障碍物设计**：合理放置墙壁，创造有趣的推理挑战
4. **解决方案唯一性**：确保每个关卡都有明确的解决路径
5. **无死局设计**：避免箱子被推到无法移动的位置

### 状态管理
- **初始状态保存**：关卡加载时保存玩家和箱子的初始位置
- **移动历史记录**：每次移动后保存当前状态（最多50步）
- **重置功能**：一键恢复到关卡初始状态
- **回退功能**：逐步回退到之前的状态

## 🔧 技术实现

### 安全区域适配
```swift
// 使用safeAreaLayoutGuide确保UI不被刘海遮挡
NSLayoutConstraint.activate([
    skView.topAnchor.constraint(equalTo: self.view.safeAreaLayoutGuide.topAnchor),
    // ... 其他约束
])
```

### 动态瓦片大小
```swift
// 根据关卡大小和屏幕尺寸动态调整
let availableWidth = self.size.width - 40
let availableHeight = self.size.height - safeAreaInsets.top - safeAreaInsets.bottom - 120
let maxTileWidth = availableWidth / CGFloat(levelWidth)
let maxTileHeight = availableHeight / CGFloat(levelHeight)
tileSize = min(maxTileWidth, maxTileHeight, 80)
```

### 音效处理
```swift
func playSound(_ fileName: String) {
    if let path = Bundle.main.path(forResource: fileName.replacingOccurrences(of: ".wav", with: ""), ofType: "wav") {
        // 播放自定义音效
        let soundAction = SKAction.playSoundFileNamed(fileName, waitForCompletion: false)
        self.run(soundAction)
    } else {
        // 使用系统音效作为回退
        AudioServicesPlaySystemSound(1057)
    }
}
```


### 即将实现的功能
1. **穿山甲广告集成**：
   - 激励视频广告：观看广告获得提示
   - 插屏广告：关卡间展示
   - IDFA集成：用户追踪和个性化广告

2. **更多关卡**：
   - 扩展到50+关卡
   - 主题关卡包（森林、沙漠、太空等）
   - 用户自定义关卡编辑器

3. **社交功能**：
   - 关卡分享
   - 排行榜系统
   - 成就系统

4. **视觉增强**：
   - 粒子效果
   - 动画过渡
   - 主题皮肤

### 技术优化
- 数据持久化：保存游戏进度
- 性能优化：大关卡的渲染优化
- 网络功能：在线关卡下载
- 多语言支持：国际化

## 📱 系统要求

- iOS 12.0+
- iPhone/iPad 兼容
- 支持所有屏幕尺寸
- 安全区域自适应

## 🎨 设计理念

SuperSokoban 致力于创造一个既经典又现代的推箱子游戏体验。我们相信好的游戏应该：

1. **易于上手**：直观的点击控制，清晰的视觉反馈
2. **难于精通**：精心设计的关卡，需要策略思考
3. **视觉愉悦**：现代化的UI设计，舒适的色彩搭配
4. **功能完整**：完善的游戏功能，流畅的用户体验

通过不断的迭代和优化，我们希望为玩家提供最佳的推箱子游戏体验。

## 🔄 版本更新日志

### v1.0.0 (当前版本)
- ✅ 完成基础游戏逻辑
- ✅ 实现5个精心设计的关卡
- ✅ 添加点击控制和移动路径显示
- ✅ 完善UI界面和安全区域适配
- ✅ 集成音效系统和错误处理
- ✅ 实现重置和回退功能
- ✅ 添加关卡选择菜单

### 下一版本计划 (v1.2.0)
- 🔄 集成穿山甲广告SDK和IDFA
- 🔄 实现数据持久化和游戏进度保存
- 🔄 添加粒子效果和动画过渡
- 🔄 实现成就系统和排行榜
- 🔄 添加音效增强和背景音乐
- 🔄 支持多语言国际化

---

**开发团队**：致力于创造优质移动游戏体验
**技术栈**：Swift, SpriteKit, iOS SDK
**设计理念**：简单易用，功能完整，视觉精美